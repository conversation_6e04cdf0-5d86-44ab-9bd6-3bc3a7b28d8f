# Go
# Build your Go project.
# Add steps that test, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/go

pool:
  vmImage: 'vs2017-win2016'

variables:
  GOBIN:  'C:\Go\bin' # Go binaries path
  GOPATH: '$(system.defaultWorkingDirectory)\gopath' # Go workspace path
  GO111MODULE: 'on'
  modulePath: '$(GOPATH)\src\github.com\$(build.repository.name)' # Path to the module's code

steps:
- powershell: |
    choco upgrade golang
    mkdir "$env:GOPATH\pkg" | out-null
    mkdir "$env:modulePath" | out-null
    robocopy "$env:system_defaultWorkingDirectory\" "$env:modulePath\" /E /Z /ZB /R:5 /W:5 /TBD /NP /V /XD "$env:GOPATH"
    exit ($LastExitCode -band 24)
  displayName: 'set up the Go workspace'

- powershell: |
    & "C:\\Go\\bin\\go.exe" test -mod=vendor -race ./...
  workingDirectory: '$(modulePath)'
  displayName: 'run tests'
