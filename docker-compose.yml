version: '3'
services:
  dev:
    build:
      context: .
      dockerfile: cmd/proxy/Dockerfile
      args:
        GOLANG_VERSION: "1.23.5"
    environment:
      - ATHENS_MONGO_STORAGE_URL=mongodb://mongo:27017
      - TIMEOUT=20 # in case the mongo dependency takes longer to start up
      - ATHENS_STORAGE_TYPE=mongo
    ports:
      - 3000:3000
    depends_on:
      - mongo
      - jaeger
  testunit:
    build:
      context: .
      dockerfile: Dockerfile.test
      args:
        GOLANG_VERSION: "1.23.5"
    command: ["./scripts/test_unit.sh"]
    environment:
      - GO_ENV=test
      - ATHENS_MINIO_ENDPOINT=http://minio:9000
      - ATHENS_MONGO_STORAGE_URL=mongodb://mongo:27017
      - TIMEOUT=20 # in case the mongo dependency takes longer to start up
      - ATHENS_STORAGE_TYPE=mongo
    depends_on:
      - mongo
      - minio
  teste2e:
    build:
      context: .
      dockerfile: Dockerfile.test
      args:
        GOLANG_VERSION: "1.23.5"
    command: ["./scripts/test_e2e.sh"]
  azurite:
    image: arafato/azurite:2.6.5
    ports:
      - 10000:10000
    environment:
      executable: blob
  mongo:
    image: mongo:3.7.9-jessie
    ports:
      - 27017:27017
  minio:
    image: minio/minio:latest
    command: server /data
    ports:
      - "9001:9000"
    environment:
      MINIO_ACCESS_KEY: minio
      MINIO_SECRET_KEY: minio123
  datadog:
    environment:
      - DD_API_KEY=
      - DD_LOG_LEVEL=trace
      - DD_APM_ENABLED=true
    image:
      datadog/agent:latest
    ports:
      - 8126:8126
  jaeger:
    environment:
      - COLLECTOR_ZIPKIN_HTTP_PORT=9441
    image: jaegertracing/all-in-one:latest
    ports:
      - 14268:14268
      - 9411:9411
      - 5775:5775/udp
      - 6831:6831/udp
      - 6832:6832/udp
      - 5778:5778
      - 16686:16686
  redis:
    image: redis
    ports:
      - 6379:6379
  redis-sentinel:
    image: bitnami/redis-sentinel
    environment:
      - REDIS_MASTER_HOST=redis
      - REDIS_MASTER_SET=redis-1
      - REDIS_SENTINEL_PASSWORD=sekret
      - REDIS_SENTINEL_QUORUM=1
    ports:
      - 26379:26379
    depends_on:
      - "redis"
  protectedredis:
    image: redis
    ports:
      - "6380:6380"
    volumes:
      - "./test/redis.conf:/usr/local/etc/redis/redis.conf"
    entrypoint: ["redis-server", "/usr/local/etc/redis/redis.conf"]
  etcd0:
    image: quay.io/coreos/etcd
    ports:
      - "2379:2379"
    volumes:
      - etcd0:/etcd_data
    command:
      - /usr/local/bin/etcd
      - -name
      - etcd0
      - --data-dir
      - /etcd_data
      - -advertise-client-urls
      - http://etcd0:2379
      - -listen-client-urls
      - http://0.0.0.0:2379
      - -initial-advertise-peer-urls
      - http://etcd0:2380
      - -listen-peer-urls
      - http://0.0.0.0:2380
      - -initial-cluster
      - etcd0=http://etcd0:2380,etcd1=http://etcd1:2380,etcd2=http://etcd2:2380
  etcd1:
    image: quay.io/coreos/etcd
    ports:
      - "22379:2379"
    volumes:
      - etcd1:/etcd_data
    command:
      - /usr/local/bin/etcd
      - -name
      - etcd1
      - --data-dir
      - /etcd_data
      - -advertise-client-urls
      - http://etcd1:2379
      - -listen-client-urls
      - http://0.0.0.0:2379
      - -initial-advertise-peer-urls
      - http://etcd1:2380
      - -listen-peer-urls
      - http://0.0.0.0:2380
      - -initial-cluster
      - etcd0=http://etcd0:2380,etcd1=http://etcd1:2380,etcd2=http://etcd2:2380
  etcd2:
    image: quay.io/coreos/etcd
    ports:
      - "32379:2379"
    volumes:
      - etcd2:/etcd_data
    command:
      - /usr/local/bin/etcd
      - -name
      - etcd2
      - --data-dir
      - /etcd_data
      - -advertise-client-urls
      - http://etcd2:2379
      - -listen-client-urls
      - http://0.0.0.0:2379
      - -initial-advertise-peer-urls
      - http://etcd2:2380
      - -listen-peer-urls
      - http://0.0.0.0:2380
      - -initial-cluster
      - etcd0=http://etcd0:2380,etcd1=http://etcd1:2380,etcd2=http://etcd2:2380

volumes:
  etcd0:
  etcd1:
  etcd2:
