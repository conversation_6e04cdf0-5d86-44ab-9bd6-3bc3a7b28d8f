<span class="github-buttons"></span>
<p>Built with <a href="https://github.com/McShelby/hugo-theme-relearn" title="love"><i class="fas fa-heart"></i></a> by <a href="https://gohugo.io/">Hugo</a></p>
<script>
  function githubButtonsScheme(){
    var scheme = 'light';
    var colorPropertyValue = window.getComputedStyle( document.querySelector( '#R-sidebar' ) ).getPropertyValue( 'background-color' );
    var colorValues = colorPropertyValue.match( /\d+/g ).map( function( e ){ return parseInt(e,10); });
    if( colorValues.length === 3 && ((0.2126 * colorValues[0]) + (0.7152 * colorValues[1]) + (0.0722 * colorValues[2]) < 165) ){
      // <PERSON><PERSON>, https://en.wikipedia.org/wiki/Luma_%28video%29, SMPTE C, Rec. 709 weightings
      scheme = 'dark';
    }
    return scheme;
  }
  function githubButtonsInit(){
    if( !window.githubButtons ){
      // wait for the script to load
      setTimeout( githubButtonsInit, 50 );
      return;
    }
    var scheme = githubButtonsScheme();
    var githubButtonsHTML = `
      <a class="github-button" href="https://github.com/gomods/athens/releases" data-color-scheme="${scheme}" data-icon="octicon-cloud-download" aria-label="Download gomods/athens on GitHub">Download</a>
      <a class="github-button" href="https://github.com/gomods/athens" data-color-scheme="${scheme}" data-icon="octicon-star" data-show-count="true" aria-label="Star gomods/athens on GitHub">Star</a>
      <a class="github-button" href="https://github.com/gomods/athens/fork" data-color-scheme="${scheme}" data-icon="octicon-repo-forked" data-show-count="true" aria-label="Fork gomods/athens on GitHub">Fork</a>
     `;
    document.querySelector( '.github-buttons' ).innerHTML = githubButtonsHTML;
    document.querySelectorAll( '.github-button' ).forEach( function( anchor ){
      anchor.dataset.colorScheme = scheme;
      window.githubButtons.render( anchor, function( el ){
        anchor.parentNode.replaceChild( el, anchor );
      });
    });
  }
  document.addEventListener( 'themeVariantLoaded', function( e ){
    // we have to wait until the CSS class .default-animation has settled and the buttons have loaded to recalculate the scheme again
    setTimeout( githubButtonsInit, 400 );
  });
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
    setTimeout( githubButtonsInit, 400 );
  });
</script>
<script async src="{{"js/github-buttons.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>