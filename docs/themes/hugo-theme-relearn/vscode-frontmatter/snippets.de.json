{"frontMatter.content.snippets": {"Badge": {"body": ["{{% badge title=\"[[&title]]\" style=\"[[&style]]\" color=\"[[&color]]\" icon=\"[[&icon]]\" %}}[[&content]]{{% /badge %}}"], "description": "<PERSON><PERSON>gt einen kleinen Marker im Text an", "fields": [{"default": "FM_SELECTED_TEXT", "name": "content", "title": "Text", "type": "string"}, {"default": "", "name": "title", "title": "Titel", "type": "string"}, {"choices": ["", "info", "note", "tip", "warning", "primary", "secondary", "accent", "blue", "green", "gray", "orange", "red", "default", "transparent", "code"], "default": "", "name": "style", "title": "Stil-<PERSON>hema", "type": "choice"}, {"default": "", "name": "color", "title": "CSS-Farbwert", "type": "string"}, {"default": "", "name": "icon", "title": "Font Awesome Icon", "type": "string"}]}, "Button": {"body": ["{{% button href=\"[[&href]]\" target=\"[[&target]]\" type=\"[[&type]]\" style=\"[[&style]]\" color=\"[[&color]]\" icon=\"[[&icon]]\" iconposition=\"[[&iconposition]]\" %}}[[&content]]{{% /button %}}"], "description": "Zeigt eine anklickbare Schaltfläche an", "fields": [{"default": "FM_SELECTED_TEXT", "name": "content", "title": "Text", "type": "string"}, {"default": "", "name": "href", "title": "Ziel-URL", "type": "string"}, {"default": "", "name": "target", "title": "HTML a[target] wie z.B. `_self` oder `_blank`", "type": "string"}, {"default": "", "name": "type", "title": "HTML button[type] wie z.B. `button` oder `submit`", "type": "string"}, {"choices": ["", "info", "note", "tip", "warning", "primary", "secondary", "accent", "blue", "green", "gray", "orange", "red", "default", "transparent", "code"], "default": "", "name": "style", "title": "Stil-<PERSON>hema", "type": "choice"}, {"default": "", "name": "color", "title": "CSS-Farbwert", "type": "string"}, {"default": "", "name": "icon", "title": "Font Awesome Icon", "type": "string"}, {"choices": ["", "left", "right"], "default": "", "name": "iconposition", "title": "Position des Icons", "type": "choice"}]}, "Children": {"body": ["{{% children description=\"[[&description]]\" depth=\"[[&depth]]\" sort=\"[[&sort]]\" showhidden=\"[[&showhidden]]\" containerstyle=\"[[&containerstyle]]\" style=\"[[&style]]\" %}}"], "description": "Zeigt eine Liste der Unterseiten dieser Seite an", "fields": [{"choices": ["", "false", "true"], "default": "", "name": "description", "title": "Unterseiten-Beschreibung anzeigen", "type": "choice"}, {"default": "", "name": "depth", "title": "Tiefe der anzuzeigenden Unterseiten-Ebenen", "type": "string"}, {"default": "", "name": "sort", "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"choices": ["", "false", "true"], "default": "", "name": "showhidden", "title": "Versteckte Unterseiten anzeigen", "type": "choice"}, {"default": "", "name": "containerstyle", "title": "Container-HTML-Element", "type": "string"}, {"default": "", "name": "style", "title": "Container-Kind-HTML-Element", "type": "string"}]}, "Expand": {"body": ["{{% expand title=\"[[&title]]\" open=\"[[&open]]\" %}}[[&content]]{{% /expand %}}"], "description": "<PERSON>eigt einen ein-/ausklappbaren Textblock an", "fields": [{"default": "FM_SELECTED_TEXT", "name": "content", "title": "Text", "type": "string"}, {"default": "", "name": "title", "title": "Titel", "type": "string"}, {"choices": ["", "false", "true"], "default": "", "name": "open", "title": "Textblock initial ausklappen", "type": "choice"}]}, "Highlight": {"body": ["````[[&type]] title=\"[[&title]]\" wrap=\"[[&wrap]]\" lineNos=\"[[&lineNos]]\" anchorLineNos=\"[[&anchorLineNos]]\" lineAnchors=\"[[&lineAnchors]]\" lineNoStart=\"[[&lineNoStart]]\" hl_Lines=\"[[&hl_Lines]]\"", "[[&content]]", "````"], "description": "Zeigt formatierten Code an", "fields": [{"default": "FM_SELECTED_TEXT", "name": "content", "title": "Text", "type": "string"}, {"name": "title", "title": "Titel", "type": "string"}, {"choices": ["", "false", "true"], "default": "", "name": "wrap", "title": "Zeilen umbrechen", "type": "choice"}, {"choices": ["", "false", "true"], "default": "", "name": "lineNos", "title": "Zeilennummern anzeigen", "type": "choice"}, {"choices": ["", "false", "true"], "default": "", "name": "anchorLineNos", "title": "Zeilennummern verlinkbar", "type": "choice"}, {"default": "", "name": "lineAnchors", "title": "Eindeutiges Prefix für verlinkbare Zeilennummern", "type": "string"}, {"default": "", "name": "lineNoStart", "title": "<PERSON>rste Zeilennummer", "type": "string"}, {"default": "", "name": "hl_Lines", "title": "<PERSON><PERSON> mark<PERSON>", "type": "string"}]}, "Icon": {"body": ["{{% icon [[&icon]] %}}"], "description": "<PERSON><PERSON><PERSON> ein Font Awesome Icon an", "fields": [{"name": "icon", "title": "Font Awesome Icon", "type": "string"}]}, "Include": {"body": ["{{% include file=\"[[&file]]\" hidefirstheading=\"[[&hidefirstheading]]\" %}}"], "description": "<PERSON><PERSON><PERSON> den Inhalt einer Datei an", "fields": [{"name": "file", "title": "Pfad zur Datei", "type": "string"}, {"choices": ["", "false", "true"], "default": "", "name": "hidefirstheading", "title": "Erste Überschrift verstecken", "type": "choice"}]}, "Math": {"body": ["````math align=\"[[&align]]\"", "[[&content]]", "````"], "description": "Zeigt eine mathematische oder chemische Formel mittels MathJax an", "fields": [{"default": "FM_SELECTED_TEXT", "name": "content", "title": "Formel", "type": "string"}, {"choices": ["", "left", "center", "right"], "default": "", "name": "align", "title": "Horizontale Ausrichtung", "type": "choice"}]}, "Mermaid": {"body": ["````mermaid align=\"[[&align]]\" zoom=\"[[&zoom]]\"", "[[&content]]", "````"], "description": "<PERSON><PERSON><PERSON> ein Mermaid Diagramm an", "fields": [{"default": "FM_SELECTED_TEXT", "name": "content", "title": "Diagramm", "type": "string"}, {"choices": ["", "left", "center", "right"], "default": "", "name": "align", "title": "Horizontale Ausrichtung", "type": "choice"}, {"choices": ["", "false", "true"], "default": "", "name": "zoom", "title": "Diagramm schieb- und zoombar", "type": "choice"}]}, "Notice": {"body": ["{{% notice title=\"[[&title]]\" style=\"[[&style]]\" color=\"[[&color]]\" icon=\"[[&icon]]\" %}}[[&content]]{{% /notice %}}"], "description": "Zeigt eine konfigurierbare Text-Box an", "fields": [{"default": "FM_SELECTED_TEXT", "name": "content", "title": "Text", "type": "string"}, {"default": "", "name": "title", "title": "Titel", "type": "string"}, {"choices": ["", "info", "note", "tip", "warning", "primary", "secondary", "accent", "blue", "green", "gray", "orange", "red", "default", "transparent", "code"], "default": "", "name": "style", "title": "Stil-<PERSON>hema", "type": "choice"}, {"default": "", "name": "color", "title": "CSS-Farbwert", "type": "string"}, {"default": "", "name": "icon", "title": "Font Awesome Icon", "type": "string"}]}, "OpenAPI": {"body": ["{{< openapi src=\"[[&src]]\" >}}"], "description": "<PERSON><PERSON>gt eine OpenAPI / Swagger Spezifikation an", "fields": [{"name": "src", "title": "URL zur OpenAPI Specifikation", "type": "string"}]}, "Resources": {"body": ["{{% resources title=\"[[&title]]\" pattern=\"[[&pattern]]\" sort=\"[[&sort]]\" style=\"[[&style]]\" color=\"[[&color]]\" icon=\"[[&icon]]\" /%}}"], "description": "<PERSON><PERSON><PERSON> eine Liste von Resourcen eines Page Bundles an", "fields": [{"default": "", "name": "title", "title": "Titel", "type": "string"}, {"default": "", "name": "pattern", "title": "Regex zum Filtern der Dateinamen", "type": "string"}, {"choices": ["", "asc", "desc"], "default": "", "name": "sort", "title": "Ausgabereihenfolge", "type": "choice"}, {"choices": ["", "info", "note", "tip", "warning", "primary", "secondary", "accent", "blue", "green", "gray", "orange", "red", "default", "transparent", "code"], "default": "", "name": "style", "title": "Stil-<PERSON>hema", "type": "choice"}, {"default": "", "name": "color", "title": "CSS-Farbwert", "type": "string"}, {"default": "", "name": "icon", "title": "Font Awesome Icon", "type": "string"}]}, "Site Param": {"body": ["{{% siteparam name=\"[[&name]]\" %}}"], "description": "<PERSON><PERSON><PERSON> einen \"site parameter\" an", "fields": [{"name": "name", "title": "Name des \"site parameter\"", "type": "string"}]}, "Tab": {"body": ["{{% tab title=\"[[&title]]\" style=\"[[&style]]\" color=\"[[&color]]\" icon=\"[[&icon]]\" %}}", "[[&content]]", "{{% /tab %}}"], "description": "<PERSON><PERSON><PERSON> einen einzelnen Ta<PERSON> an", "fields": [{"default": "FM_SELECTED_TEXT", "name": "content", "title": "Text", "type": "string"}, {"name": "title", "title": "Titel", "type": "string"}, {"choices": ["", "info", "note", "tip", "warning", "primary", "secondary", "accent", "blue", "green", "gray", "orange", "red", "default", "transparent", "code"], "default": "", "name": "style", "title": "Stil-<PERSON>hema", "type": "choice"}, {"default": "", "name": "color", "title": "CSS-Farbwert", "type": "string"}, {"default": "", "name": "icon", "title": "Font Awesome Icon", "type": "string"}]}, "Tabs": {"body": ["{{< tabs title=\"[[&title]]\" style=\"[[&style]]\" color=\"[[&color]]\" icon=\"[[&icon]]\" groupid=\"[[&groupid]]\" >}}", "{{% tab title=\"[[&title1]]\" style=\"[[&style1]]\" color=\"[[&color1]]\" icon=\"[[&icon1]]\" %}}", "[[&content1]]", "{{% /tab %}}", "{{% tab title=\"[[&title2]]\" style=\"[[&style2]]\" color=\"[[&color2]]\" icon=\"[[&icon2]]\" %}}", "[[&content2]]", "{{% /tab %}}", "{{< /tabs >}}"], "description": "<PERSON><PERSON><PERSON> eine Tabgruppe an", "fields": [{"default": "", "name": "title", "title": "Titel der Tabgruppe", "type": "string"}, {"choices": ["", "info", "note", "tip", "warning", "primary", "secondary", "accent", "blue", "green", "gray", "orange", "red", "default", "transparent", "code"], "default": "", "name": "style", "title": "Standard-Stil-<PERSON><PERSON><PERSON> aller Tabs", "type": "choice"}, {"default": "", "name": "color", "title": "Standard-CSS-Farbwert aller Tabs", "type": "string"}, {"default": "", "name": "icon", "title": "Font Awesome Icon der Tabgruppe", "type": "string"}, {"default": "", "name": "groupid", "title": "ID, die zum synchronisieren der Tabauswahl über mehrere Tabgruppen hinweg benutzt werden soll", "type": "string"}, {"default": "FM_SELECTED_TEXT", "name": "content1", "title": "Text des ersten Tabs", "type": "string"}, {"name": "title1", "title": "Titel des ersten Tabs", "type": "string"}, {"choices": ["", "info", "note", "tip", "warning", "primary", "secondary", "accent", "blue", "green", "gray", "orange", "red", "default", "transparent", "code"], "default": "", "name": "style1", "title": "Stil-Schema des ersten Tabs", "type": "choice"}, {"default": "", "name": "color1", "title": "CSS-Farbwert des ersten Tabs", "type": "string"}, {"default": "", "name": "icon1", "title": "Font Awesome Icon des ersten Tabs", "type": "string"}, {"default": "", "name": "content2", "title": "Text des zweiten Tabs", "type": "string"}, {"name": "title2", "title": "Titel des zweiten Tabs", "type": "string"}, {"choices": ["", "info", "note", "tip", "warning", "primary", "secondary", "accent", "blue", "green", "gray", "orange", "red", "default", "transparent", "code"], "default": "", "name": "style2", "title": "Stil-Schema des zweiten Tabs", "type": "choice"}, {"default": "", "name": "color2", "title": "CSS-Farbwert des zweiten Tabs", "type": "string"}, {"default": "", "name": "icon2", "title": "Font Awesome Icon des zweiten Tabs", "type": "string"}]}}}