# Hugo Relearn Theme

A theme for [<PERSON>](https://gohugo.io/) designed for documentation.

[★ What's new in the latest release ★](https://mcshelby.github.io/hugo-theme-relearn/basics/migration)

![Image of the Relearn theme in light and dark mode on phone, tablet and desktop](https://github.com/McShelby/hugo-theme-relearn/raw/main/images/hero.png)

## Motivation

The Relearn theme is a fork of the great [Learn theme](https://github.com/matcornic/hugo-theme-learn) with the aim of fixing long outstanding bugs and adapting to latest Hugo features. As far as possible this theme tries to be a drop-in replacement for the Learn theme.

## Features

- **Wide set of usage scenarios**
  - Responsive design for mobile usage
  - Looks nice on paper (if it has to)
  - Usable offline, no external dependencies
  - [Usable from your local file system via `file://` protocol](https://mcshelby.github.io/hugo-theme-relearn/basics/customization#file-system)
  - Support for the [VSCode Front Matter extension](https://github.com/estruyf/vscode-front-matter) for on-premise CMS capabilities
  - [Support for Open Graph and Twitter Cards](https://mcshelby.github.io/hugo-theme-relearn/basics/customization#social-media-meta-tags)
- **Configurable theming and visuals**
  - [Configurable brand images](https://mcshelby.github.io/hugo-theme-relearn/basics/branding#change-the-logo)
  - [Automatic switch for light/dark variant depending on your OS settings](https://mcshelby.github.io/hugo-theme-relearn/basics/branding#adjust-to-os-settings)
  - Predefined light, dark and color variants
  - [User selectable variants](https://mcshelby.github.io/hugo-theme-relearn/basics/branding#multiple-variants)
  - [Stylesheet generator](https://mcshelby.github.io/hugo-theme-relearn/basics/generator)
  - [Configurable syntax highlighting](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/highlight)
- **Unique theme features**
  - [Print whole chapters or even the complete site](https://mcshelby.github.io/hugo-theme-relearn/basics/customization#activate-print-support)
  - In page search
  - [Site search](https://mcshelby.github.io/hugo-theme-relearn/basics/customization#activate-search)
  - [Dedicated search page](https://mcshelby.github.io/hugo-theme-relearn/basics/customization#activate-dedicated-search-page)
  - [Taxonomy support](https://mcshelby.github.io/hugo-theme-relearn/cont/taxonomy)
  - [Configurable topbar buttons](https://mcshelby.github.io/hugo-theme-relearn/basics/topbar)
  - [Unlimited nested menu items](https://mcshelby.github.io/hugo-theme-relearn/cont/pages)
  - [Configurable shortcut links](https://mcshelby.github.io/hugo-theme-relearn/cont/menushortcuts)
  - Hidden pages
- **Multi language support**
  - [Full support for languages written right to left](https://mcshelby.github.io/hugo-theme-relearn/cont/i18n)
  - [Available languages](https://mcshelby.github.io/hugo-theme-relearn/cont/i18n#basic-configuration): Arabic, Simplified Chinese, Traditional Chinese, Czech, Dutch, English, Finnish, French, German, Hindi, Hungarian, Indonesian, Italian, Japanese, Korean, Polish, Portuguese, Romanian, Russian, Spanish, Swahili, Turkish, Vietnamese
  - [Search support for mixed language content](https://mcshelby.github.io/hugo-theme-relearn/cont/i18n#search)
- **Additional Markdown features**
  - [Support for GFM (GitHub Flavored Markdown)](https://mcshelby.github.io/hugo-theme-relearn/cont/markdown)
  - [Image effects like sizing, shadow, border and alignment](https://mcshelby.github.io/hugo-theme-relearn/cont/markdown#image-effects)
  - [Image lightbox](https://mcshelby.github.io/hugo-theme-relearn/cont/markdown#lightbox)
- **Shortcodes galore**
  - [Display resources contained in a page bundle](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/resources)
  - [Marker badges](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/badge)
  - [Configurable buttons](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/button)
  - [List child pages](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/children)
  - [Expand areas to reveal content](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/expand)
  - [Font Awesome icons](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/icon)
  - [Inclusion of other files](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/include)
  - [Math and chemical formulae using MathJax](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/math)
  - [Mermaid diagrams for flowcharts, sequences, gantts, pie, etc.](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/mermaid)
  - [Colorful boxes](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/notice)
  - [OpenAPI specifications using Swagger UI](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/openapi)
  - [Reveal you site's configuration parameter](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/siteparam)
  - [Single tabbed panels](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/tab) and [multiple tabbed panels](https://mcshelby.github.io/hugo-theme-relearn/shortcodes/tabs)

## Installation & Usage

For a detailed description of the theme's capabilities visit the [official documentation](https://mcshelby.github.io/hugo-theme-relearn/).

## Changelog

See the [What's New](https://mcshelby.github.io/hugo-theme-relearn/basics/migration) page for release highlights or the detailed [change history](https://mcshelby.github.io/hugo-theme-relearn/basics/history) for a complete list of changes.

## Contributions

You are most welcome to contribute bugfixes or new features. Check the [contribution guidelines](https://mcshelby.github.io/hugo-theme-relearn/dev/contributing) first before starting.

## License

The Relearn theme is licensed under the [MIT License](https://github.com/McShelby/hugo-theme-relearn/blob/main/LICENSE).

## Credits

This theme would not be possible without the work of [many others](https://mcshelby.github.io/hugo-theme-relearn/more/credits).
