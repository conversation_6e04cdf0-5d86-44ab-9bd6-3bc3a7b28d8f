{{- partialCached "page-meta.hugo" . .RelPermalink }}
{{- printf "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"yes\" ?>" | safeHTML }}
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
{{- range where .Pages "Sitemap.Disable" "ne" true }}
{{- if and .Title .RelPermalink (or (ne (.Scratch.Get "relearnIsHiddenStem") true) (ne .Site.Params.disableSeoHiddenPages true) ) }}
  <url>
    <loc>{{ partial "relLangPrettyUglyURL.hugo" (dict "to" . "abs" true) }}</loc>{{ if not .Lastmod.IsZero }}
    <lastmod>{{ safeHTML ( .Lastmod.Format "2006-01-02T15:04:05-07:00" ) }}</lastmod>{{ end }}{{ with .Sitemap.ChangeFreq }}
    <changefreq>{{ . }}</changefreq>{{ end }}{{ if ge .Sitemap.Priority 0.0 }}
    <priority>{{ .Sitemap.Priority }}</priority>{{ end }}{{ if .IsTranslated }}{{ range $index, $trans := .AllTranslations }}{{- if eq $index 0 }}
    <xhtml:link rel="alternate" hreflang="x-default" href="{{ partial "relLangPrettyUglyURL.hugo" (dict "to" . "abs" true) }}" />{{ end }}
    <xhtml:link rel="alternate" hreflang="{{ .Language.LanguageCode }}" href="{{ partial "relLangPrettyUglyURL.hugo" (dict "to" . "abs" true) }}" />{{ end }}{{ end }}
  </url>
{{- end -}}
{{- end }}
</urlset>
