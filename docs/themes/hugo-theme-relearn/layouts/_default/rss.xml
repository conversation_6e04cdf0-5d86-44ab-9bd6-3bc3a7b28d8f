{{- partialCached "page-meta.hugo" . .RelPermalink }}
{{- /* Deprecate site.Author.email in favor of site.Params.author.email */}}
{{- $authorEmail := "" }}
{{- if and .Site.Params.author (reflect.IsMap .Site.Params.author) .Site.Params.author.email }}
  {{- $authorEmail = .Site.Params.author.email }}
{{- else }}
  {{- with .Site.Author.email }}
    {{- $authorEmail = . }}
    {{- $hugoVersion := "0.120.0" }}
    {{- if ge hugo.Version $hugoVersion }}
      {{- warnf "The author key in site configuration is deprecated. Use params.author.email instead." }}
    {{- end }}
  {{- end }}
{{- end }}

{{- /* Deprecate site.Author.name in favor of site.Params.author.name */}}
{{- $authorName := partialCached "authorname.hugo" . }}

{{- $pages := .Pages }}
{{- $limit := .Site.Config.Services.RSS.Limit }}
{{- if ge $limit 1 }}
  {{- $pages = $pages | first $limit }}
{{- end }}

{{- $page := . }}
{{- printf "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"yes\"?>" | safeHTML }}
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>{{ if eq .Title .Site.Title }}{{ .Site.Title }}{{ else }}{{ with .Title }}{{ . }} on {{ end }}{{ .Site.Title }}{{ end }}</title>
    <link>{{ partial "relLangPrettyUglyURL.hugo" (dict "to" . "abs" true) }}</link>
    <description>Recent content {{ if ne .Title .Site.Title }}{{ with .Title }}in {{ . }} {{ end }}{{ end }}on {{ .Site.Title }}</description>
    <generator>Hugo -- gohugo.io</generator>{{ with .Site.LanguageCode }}
    <language>{{ . }}</language>{{end}}{{ with $authorEmail }}
    <managingEditor>{{ . }}{{ with $authorName }} ({{ . }}){{ end }}</managingEditor>{{ end }}{{ with $authorEmail }}
    <webMaster>{{ . }}{{ with $authorName }} ({{ . }}){{ end }}</webMaster>{{ end }}{{ with .Site.Copyright }}
    <copyright>{{ . }}</copyright>{{ end }}{{ if not .Date.IsZero }}
    <lastBuildDate>{{ (index $pages.ByLastmod.Reverse 0).Lastmod.Format "Mon, 02 Jan 2006 15:04:05 -0700" | safeHTML }}</lastBuildDate>{{ end }}
    {{- with .OutputFormats.Get "rss" }}
    {{ printf "<atom:link href=%q rel=\"self\" type=%q />" ( partial "relLangPrettyUglyURL.hugo" (dict "to" . "abs" true)) .MediaType | safeHTML }}
    {{- end -}}
    {{- range $pages }}
      {{- $relearnIsHiddenFrom := index ($page.Scratch.Get "relearnIsHiddenFrom") .RelPermalink }}
  	  {{- if and .Permalink .Title (or (not $relearnIsHiddenFrom) (ne .Site.Params.disableSeoHiddenPages true) ) }}
    <item>
      <title>{{ .Title }}</title>
      <link>{{ partial "relLangPrettyUglyURL.hugo" (dict "to" . "abs" true) }}</link>
      <pubDate>{{ .PublishDate.Format "Mon, 02 Jan 2006 15:04:05 -0700" | safeHTML }}</pubDate>
      {{- with $authorEmail }}<author>{{ . }}{{ with $authorName }} ({{ . }}){{ end }}</author>{{ end }}
      <guid>{{ partial "relLangPrettyUglyURL.hugo" (dict "to" . "abs" true) }}</guid>
      <description>{{ .Summary | transform.XMLEscape | safeHTML }}</description>
    </item>
      {{- end }}
    {{- end }}
  </channel>
</rss>