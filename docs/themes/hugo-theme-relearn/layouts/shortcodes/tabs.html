{{- $unused := .Inner }}
{{- $color   := (.Get "color") }}
{{- $groupid := ((.Get "groupid") | default (.Get "groupId")) }}
{{- if (.Get "groupId") }}
  {{- warnf "%q: DEPRECATED parameter 'groupId' for shortcode 'tabs' found, use 'groupid' instead" .Page.File.Filename }}
{{- end }}
{{- $icon    := (.Get "icon") }}
{{- $style   := (.Get "style") }}
{{- $title   := (.Get "title") }}
{{- $tabs    := (.Scratch.Get "tabs") }}
{{- partial "shortcodes/tabs.html" (dict
  "page"    .Page
  "color"   $color
  "content" $tabs
  "groupid" $groupid
  "icon"    $icon
  "style"   $style
  "title"   $title
) }}