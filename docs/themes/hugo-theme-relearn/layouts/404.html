<!DOCTYPE html>
{{- partialCached "page-meta.hugo" . .RelPermalink }}
{{- $outputFormat := partial "output-format.hugo" . }}
<html lang="{{ .Page.Language.LanguageCode | default "en" }}" dir="{{ .Page.Language.LanguageDirection | default (T "Reading-direction") | default "ltr" }}">
  <head>
    {{- partial "meta.html" . }}
    {{- $title := partial "pageHelper/title.hugo" (dict "page" . "fullyQualified" true "reverse" true) }}
    <title>{{ $title }}</title>
    <base href="{{ .Site.BaseURL }}">
    {{- partialCached "favicon.html" . }}
    {{- partial "stylesheet.html" (dict "page" . "outputFormat" $outputFormat) }}
    <style>
      p {
        text-align: center
      }
      .notfound #R-body {
          margin-inline-start: 0;
          max-width: 100%;
          min-width: 100%;
          width: 100%;
      }
      .notfound h1 {
          color: var(--MAIN-TEXT-color);
          line-height: 1;
          font-size: 5rem;
          overflow: hidden;
      }
      .notfound h1 span {
          font-size: 6.5rem;
          font-weight: 500;
      }
      .notfound h1 i {
          font-size: 5rem;
          vertical-align: text-top;
      }
      .notfound h2 {
        color: var(--MAIN-TEXT-color);
        font-size: 2.5rem;
        font-weight: 500;
        padding: 0;
        text-align: center;
      }
      #shrug svg,
      #shrug svg * {
        color: #000000;
        color: var(--MAIN-TEXT-color);
        fill: #000000 !important;
        fill: var(--MAIN-TEXT-color) !important;
      }
      #shrug svg{
        transform: scaleX(-1);
        width: 15rem;
      }
    </style>
    {{- partial "custom-header.html" . }}
  </head>
  <body class="mobile-support html notfound" data-url="{{ partial "relLangPrettyUglyURL.hugo" (dict "to" .) }}">
    <div id="R-body" class="default-animation">
      <main id="R-body-inner" class="chapter" tabindex="-1">
        <div class="flex-block-wrapper">
          <article>
            <h1 id="404"><span>4</span>{{ partial "shortcodes/icon.html" (dict "page" . "icon" "far fa-frown" )}}<span>4</span></h1>
            <h2 id="{{ T "title-404" | plainify | anchorize }}">{{ T "title-404" }}</h2>
            <p></p>
            <p>{{ T "message-404" }}</p>
            <p></p>
            <p><a href="{{ partial "relLangPrettyUglyURL.hugo" (dict "to" .Site.Home) }}">{{ T "Go-to-homepage" }}</a></p>
            <p id="shrug">
              <svg xmlns="http://www.w3.org/2000/svg"
 viewBox="0 0 512.000000 512.000000"
 preserveAspectRatio="xMidYMid meet">

<g transform="translate(0.000000,512.000000) scale(0.100000,-0.100000)"
style="fill:#000000;stroke:none;">
<path d="M2359 4821 c-330 -72 -613 -312 -803 -681 -53 -102 -74 -133 -105
-154 -46 -32 -103 -113 -121 -170 -7 -23 -11 -72 -8 -120 3 -65 10 -91 33
-134 36 -66 91 -117 161 -149 30 -13 54 -30 54 -38 0 -33 56 -199 94 -281 54
-115 110 -196 192 -283 35 -38 64 -71 64 -74 0 -3 -17 -11 -37 -18 -57 -18
-170 -77 -238 -122 -75 -52 -213 -184 -270 -259 -51 -68 -46 -74 -75 101 -12
68 -32 150 -45 182 -65 160 -179 253 -342 279 -125 20 -234 85 -376 224 -103
101 -149 127 -233 134 -115 9 -217 -48 -272 -152 -38 -73 -38 -177 0 -259 68
-146 246 -382 417 -554 l101 -102 96 -533 c52 -293 104 -556 113 -583 67 -191
246 -315 455 -315 138 0 251 46 347 142 l49 49 0 -256 c0 -208 3 -263 15 -293
20 -47 50 -79 97 -103 36 -18 71 -19 838 -19 780 0 802 1 840 20 44 22 82 68
99 119 7 22 11 122 11 278 l0 244 49 -49 c96 -96 209 -142 347 -142 209 0 388
124 455 315 9 27 61 290 113 583 l96 533 101 102 c171 172 349 408 417 554 38
82 38 186 0 259 -55 104 -157 161 -272 152 -84 -7 -130 -33 -233 -134 -142
-139 -251 -204 -376 -224 -163 -26 -277 -119 -342 -279 -13 -32 -33 -114 -45
-182 -29 -175 -24 -169 -75 -101 -101 134 -259 264 -423 346 l-72 36 92 93
c51 50 109 116 128 144 l35 52 57 1 c164 0 310 134 325 298 7 74 -10 147 -48
207 -26 40 -30 60 -40 168 -24 283 -91 497 -213 682 -155 232 -387 382 -706
456 -128 30 -405 35 -521 10z m426 -197 c120 -20 265 -71 362 -125 106 -60
242 -196 302 -302 70 -125 126 -307 146 -479 9 -70 13 -69 -153 -32 -360 81
-724 280 -888 486 -63 79 -100 105 -160 114 -61 9 -107 -10 -204 -83 -144
-109 -279 -163 -403 -163 l-59 0 37 68 c50 92 164 240 233 303 118 108 259
183 402 214 88 18 275 18 385 -1z m-295 -674 c108 -107 232 -191 421 -284 207
-103 534 -206 656 -206 60 0 123 -64 123 -125 0 -43 -27 -93 -62 -115 -26 -17
-43 -20 -83 -15 -134 16 -135 15 -203 -90 -69 -106 -230 -260 -332 -318 -255
-143 -537 -153 -781 -26 -242 126 -419 382 -469 679 -15 90 -43 124 -111 135
-89 15 -129 56 -129 132 0 49 18 82 58 109 30 20 40 22 154 17 103 -4 138 -2
210 16 136 32 283 108 388 199 l35 31 19 -27 c10 -15 58 -65 106 -112z m-2091
-974 c186 -176 301 -240 493 -276 119 -23 182 -109 213 -290 8 -52 42 -230 75
-395 78 -394 103 -549 95 -588 -29 -136 -171 -202 -287 -134 -25 15 -55 42
-67 60 -21 34 -54 197 -131 642 -23 132 -46 256 -52 275 -7 23 -43 65 -109
128 -220 210 -444 522 -425 595 9 37 44 67 78 67 22 0 49 -19 117 -84z m4489
54 c38 -36 39 -67 2 -138 -75 -145 -239 -351 -399 -504 -66 -63 -102 -105
-109 -128 -6 -19 -29 -143 -52 -275 -77 -445 -110 -608 -131 -642 -12 -18 -42
-45 -67 -60 -116 -68 -258 -2 -287 134 -8 39 17 194 95 588 33 165 67 343 75
395 31 181 94 267 213 290 192 36 307 100 493 276 96 91 125 103 167 64z
m-2850 -536 c56 -270 324 -461 595 -424 205 28 393 204 439 411 6 30 14 58 18
62 8 10 128 -34 190 -70 166 -96 311 -245 387 -398 57 -113 57 -117 28 -287
-56 -322 -58 -411 -13 -510 74 -161 263 -254 420 -205 45 14 47 14 38 -2 -17
-33 -87 -88 -133 -106 -68 -26 -179 -17 -240 18 -25 15 -92 78 -150 142 l-105
114 -4 97 c-3 91 -5 97 -31 120 -38 33 -96 33 -134 0 l-28 -24 -3 -476 -3
-476 -749 0 -749 0 -3 481 -3 481 -28 24 c-38 33 -96 33 -134 0 -26 -23 -28
-29 -31 -120 l-4 -97 -105 -114 c-58 -64 -125 -127 -150 -142 -61 -35 -172
-44 -240 -18 -46 18 -116 73 -133 106 -9 16 -7 16 38 2 157 -49 346 44 420
205 45 99 43 188 -13 510 -29 170 -29 174 28 287 97 197 295 373 507 451 60
22 65 19 78 -42z m421 -4 c96 -15 253 -8 346 14 33 8 62 13 64 10 11 -10 -35
-99 -74 -141 -175 -195 -480 -111 -559 154 l-7 21 73 -23 c40 -13 111 -29 157
-35z"/>
<path d="M2236 3664 c-20 -20 -26 -37 -26 -68 0 -52 20 -81 65 -96 70 -23 141
42 127 116 -13 71 -113 100 -166 48z"/>
<path d="M2865 3548 c-94 -51 -61 -188 45 -188 103 0 141 138 50 185 -34 18
-67 19 -95 3z"/>
<path d="M2308 3093 c-29 -18 -48 -52 -48 -86 0 -33 36 -86 62 -92 134 -31
266 -55 303 -55 63 0 95 32 95 92 0 82 -11 88 -230 133 -144 29 -147 29 -182
8z"/>
</g>
</svg>
            </p>
          </article>
        </div>
      </main>
    </div>
  </body>
</html>
