{{- $page := .page }}
{{- if and (not $page) .context }}
  {{- $page = .context }}
  {{- warnf "%q: DEPRECATED parameter 'context' for shortcode 'attachments' found, use 'page' instead; see https://mcshelby.github.io/hugo-theme-relearn/basics/migration#5180" $page.File.Filename }}
{{- end }}
{{ warnf "%q: DEPRECATED shortcode `attachments` found, use `resources` instead; see https://mcshelby.github.io/hugo-theme-relearn/shortcodes/attachments#migration" $page.File.Filename }}
{{- $color := .color | default "" }}
{{- $content := .content }}
{{- $style := .style | default "default" }}
{{- if and (not $color) (eq (len $color) 0) }}
  {{- $style = .style | default "transparent" }}
{{- end }}
{{- $title := .title }}
{{- if not $title }}
  {{- if eq $style "info" }}{{ $title = $style | T }}{{ end }}
  {{- if eq $style "warning" }}{{ $title = $style | T }}{{ end }}
  {{- if eq $style "note" }}{{ $title = $style | T }}{{ end }}
  {{- if eq $style "tip" }}{{ $title = $style | T }}{{ end }}
{{- end }}
{{- $title = trim ($title | default ("Attachments-label" | T)) " " }}
{{- $icon := .icon | default "" }}
{{- if and (not $icon) (eq (len $icon) 0) }}
  {{- $icon = "paperclip" }}
  {{- if eq $style "info" }}{{ $icon = default "info-circle" }}{{ end }}
  {{- if eq $style "warning" }}{{ $icon = default "exclamation-triangle" }}{{ end }}
  {{- if eq $style "note" }}{{ $icon = default "exclamation-circle" }}{{ end }}
  {{- if eq $style "tip" }}{{ $icon = default "lightbulb" }}{{ end }}
{{- end }}
{{- $icon = trim $icon " " }}
{{- if and $icon (not (findRE ".*?\\bfa-\\w.*?" $icon)) }}
  {{- $icon = printf "fa-fw fas fa-%s" $icon }}
{{- end }}
{{- $sort := .sort | default "asc" }}
{{- $pattern := .pattern | default "" }}
{{- $defaultUrlPrefix := strings.TrimRight "/" $page.RelPermalink }}
{{- if .IsTranslated -}}
  {{- $defaultLanguage := (index $page.Site.Home.AllTranslations 0).Language.Lang }}
  {{- $defaultLanguagePage := index (where $page.AllTranslations "Language.Lang" $defaultLanguage) 0 }}
  {{- $defaultUrlPrefix = strings.TrimRight "/" $defaultLanguagePage.RelPermalink }}
{{- end }}
{{- $urlPrefix := strings.TrimRight "/" $page.RelPermalink }}
{{- with $page }}
{{- if ne .BundleType "leaf" }}
  {{- warnf "%q: UNSUPPORTED usage of 'attachments' shortcode found while using Hugo >= 0.112.0, use a leaf bundle instead; see https://mcshelby.github.io/hugo-theme-relearn/shortcodes/attachments/index.html#single-language" $page.File.Filename }}
{{- end }}
<div class="box attachments cstyle {{ $style }}"{{ if $color }} style="--VARIABLE-BOX-color: {{ $color }};"{{ end }}>
  <div class="box-label">{{ if $icon }}<i class="{{ $icon }}"></i>{{ end }}{{ if and $icon $title }} {{ end }}{{ $title | .RenderString }}</div>
  <ul class="box-content attachments-files">
  {{- $fileDir := path.Dir (strings.TrimPrefix (path.Clean hugo.WorkingDir) (path.Clean .File.Filename)) }}
  {{- $fileDir = printf "%s/" (path.Clean (strings.TrimPrefix "/" $fileDir)) }}
  {{- $fileLink := $fileDir }}
  {{- $fileLink = strings.TrimPrefix "content/" $fileLink }}
  {{- $filesName := printf "%s.files" .File.BaseFileName }}
  {{- if and (eq .File.BaseFileName "index") (fileExists (printf "%sfiles" $fileDir)) }}
    {{- $c := "" }}{{/* backward compat to < 5.9.0 behavior */}}
    {{- $filesName = "files" }}
  {{- end }}
  {{- $dir := printf "%s%s" $fileDir $filesName }}
  {{- if fileExists $dir }}
    {{- range sort (readDir $dir) "Name" $sort }}
      {{- if findRE $pattern .Name}}
        {{- $size := .Size }}
        {{- $unit := "Byte-symbol" }}
        {{- if ge $size 1024 }}
          {{- $size = div $size 1024 }}
          {{- $unit = "Kilobyte-symbol" }}
        {{- end }}
        {{- if ge $size 1024 }}
          {{- $size = div $size 1024 }}
          {{- $unit = "Megabyte-symbol" }}
        {{- end }}
        {{- $unitsymbol := $unit | T }}
        {{- $link := (printf "%s%s/%s" $fileLink $filesName .Name) | relLangURL }}
        {{- $c := "" }}{{/* Hugo stores the attachments always in the default language subdirectory */}}
        {{- $link = replace $link $urlPrefix $defaultUrlPrefix 1 }}
      <li><a href="{{ $link }}">{{.Name}}</a> ({{$size}} {{$unitsymbol}})</li>
      {{- end }}
    {{- end }}
  {{- end }}
  </ul>
  {{- $content }}
</div>
{{- end }}