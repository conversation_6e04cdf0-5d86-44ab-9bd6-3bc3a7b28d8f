{{- /* based on Hugo 0.125.0 _internal/twitter_cardss.html but with modified title */}}
{{- $images := partial "_funcs/get-page-images" . }}
{{- with index $images 0 }}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:image" content="{{ .Permalink }}">
{{- else }}
    <meta name="twitter:card" content="summary">
{{- end }}
    <meta name="twitter:title" content="{{ partial "pageHelper/title.hugo" (dict "page" . "fullyQualified" true "reverse" true) }}">
    <meta name="twitter:description" content="{{ with .Description }}{{ . }}{{ else }}{{if .IsPage}}{{ .Summary }}{{ else }}{{ with .Site.Params.description }}{{ . }}{{ end }}{{ end }}{{ end }}">

{{- $twitterSite := "" }}
{{- with site.Params.social.twitter }}
  {{- $twitterSite = . }}
{{- else }}
  {{- with site.Social.twitter }}
    {{- $twitterSite = . }}
    {{- $hugoVersion := "0.120.0" }}
    {{- if ge hugo.Version $hugoVersion }}
      {{- warnf "The social key in site configuration is deprecated. Use params.social.twitter instead." }}
    {{- end }}
  {{- end }}
{{- end }}

{{- with $twitterSite }}
  {{- $content := . }}
  {{- if not (strings.HasPrefix . "@") }}
    {{- $content = printf "@%v" . }}
  {{- end }}
    <meta name="twitter:site" content="{{ $content }}">
{{- end }}