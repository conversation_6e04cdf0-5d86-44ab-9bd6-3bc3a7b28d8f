{{- $page := .page }}
{{- $location := .location }}
{{- if eq $location "footer" }}
  {{- with $page }}
    <script src="{{"js/d3/d3-color.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    <script src="{{"js/d3/d3-dispatch.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    <script src="{{"js/d3/d3-drag.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    <script src="{{"js/d3/d3-ease.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    <script src="{{"js/d3/d3-interpolate.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    <script src="{{"js/d3/d3-selection.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    <script src="{{"js/d3/d3-timer.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    <script src="{{"js/d3/d3-transition.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    <script src="{{"js/d3/d3-zoom.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    <script src="{{"js/js-yaml.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    {{- if and (isset .Params "custommermaidurl") .Params.customMermaidURL }}
    <script src="{{ .Params.customMermaidURL }}" defer></script>
    {{- else if and (isset .Site.Params "custommermaidurl") .Site.Params.customMermaidURL }}
    <script src="{{ .Site.Params.customMermaidURL }}" defer></script>
    {{- else }}
    <script src="{{"js/mermaid.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    {{- end }}
    {{- $init := "{}" }}
    {{- if isset .Params "mermaidinitialize" }}
      {{- $init = .Params.mermaidInitialize }}
    {{- else if isset .Site.Params "mermaidinitialize" }}
      {{- $init = .Site.Params.mermaidInitialize }}
    {{- end }}
    <script>
      window.themeUseMermaid = JSON.parse({{ $init }});
    </script>
  {{- end }}
{{- end }}