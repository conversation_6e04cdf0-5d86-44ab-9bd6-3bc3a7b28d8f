{{- $page := .page }}
{{- $location := .location }}
{{- if eq $location "footer" }}
  {{- with $page }}
    <script src="{{"js/js-yaml.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" defer></script>
    {{- $urlOpenapi := "" }}
    {{- $relOpenapi := "" }}
    {{- $cssInProject := false }}
    {{- if and (isset .Params "customopenapiurl") .Params.customOpenapiURL }}
      {{- $urlOpenapi = .Params.customOpenapiURL }}
      {{- $relOpenapi = .Params.customOpenapiURL }}
    {{- else if and (isset .Site.Params "customopenapiurl") .Site.Params.customOpenapiURL }}
      {{- $urlOpenapi = .Site.Params.customOpenapiURL }}
      {{- $relOpenapi = .Site.Params.customOpenapiURL }}
    {{- else }}
      {{- $urlOpenapi = printf "%s%s" ("js/swagger-ui/swagger-ui-bundle.js" | relURL) (cond .Site.Params.disableAssetsBusting "" (printf "?%d" now.Unix)) }}
      {{- $relOpenapi = printf "%s%s" ("/js/swagger-ui/swagger-ui-bundle.js") (cond .Site.Params.disableAssetsBusting "" (printf "?%d" now.Unix)) }}
      {{- $cssInProject = true }}
      {{- end }}
    <script>window.noZensmooth = true;</script>
    <script src="{{ $urlOpenapi }}" defer></script>
    {{- $urlOpenapi := replace $urlOpenapi "swagger-ui-bundle" "swagger-ui-standalone-preset" }}
    <script src="{{ $urlOpenapi }}" defer></script>
    {{- $relOpenapi := replace $relOpenapi "swagger-ui-bundle" "swagger-ui" }}
    {{- $relOpenapi := replace $relOpenapi ".js" ".css" }}
    <script>
      window.themeUseOpenapi = { css: {{ $relOpenapi }}, cssInProject: {{ $cssInProject | safeJS }}, assetsBuster: {{ cond (not .Site.Params.disableAssetsBusting) now.Unix 0 }} };
    </script>
  {{- end }}
{{- end }}