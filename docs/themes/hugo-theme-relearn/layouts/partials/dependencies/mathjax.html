{{- $page := .page }}
{{- $location := .location }}
{{- if eq $location "footer" }}
  {{- with $page }}
    {{- $init := "{}" }}
    {{- if isset .Params "mathjaxinitialize" }}
      {{- $init = .Params.mathJaxInitialize }}
    {{- else if isset .Site.Params "mathjaxinitialize" }}
      {{- $init = .Site.Params.mathJaxInitialize }}
    {{- end }}
    <script>
      function useMathJax( config ){
        window.MathJax = Object.assign( window.MathJax || {}, {
          tex: {
            inlineMath:  [['\\(', '\\)'], ['$',  '$']],  // inline
            displayMath: [['\\[', '\\]'], ['$$', '$$']], // block
          },
          options: {
            enableMenu: false // avoid translation hassle for context menu
          }
        }, config );
      }
      useMathJax( JSON.parse({{ $init }}) );
    </script>
    {{- if and (isset .Params "custommathjaxurl") .Params.customMathJaxURL }}
    <script id="MathJax-script" async src="{{ .Params.customMathJaxURL }}"></script>
    {{- else if and (isset .Site.Params "custommathjaxurl") .Site.Params.customMathJaxURL }}
    <script id="MathJax-script" async src="{{ .Site.Params.customMathJaxURL }}"></script>
    {{- else }}
    <script id="MathJax-script" async src="{{"js/mathjax/tex-mml-chtml.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>
    {{- end }}
  {{- end }}
{{- end }}