{{- $page := .page }}
{{- $taxonomy := .taxonomy }}
{{- $class := .class }}
{{- $color := .color | default "" }}
{{- $style := .style | default "" }}
{{- if and (not $style) (eq (len $style) 0) }}
  {{- $color = .color | default "var(--INTERNAL-TAG-BG-color)" }}
{{- end }}
{{- $icon := .icon | default "" }}
{{- $icon = trim $icon " " }}
{{- if and $icon (not (findRE ".*?\\bfa-\\w.*?" $icon)) }}
  {{- $icon = printf "fa-fw fas fa-%s" $icon }}
{{- end }}
{{- $taxonomy_page := $page.Site.GetPage $taxonomy }}
{{- $terms := slice | append (index $page.Params $taxonomy) }}
{{- $term_pages := slice }}
{{- range $terms }}
  {{- $term := . }}
  {{- $term_key := $term }}
  {{- with $page.Site.GetPage (printf "%s/%s" $taxonomy $term_key) }}
    {{- $term_pages = $term_pages | append (dict "Title" (default (humanize .Data.Term | strings.Title) .Title) "Term" . )}}
  {{- else }}
    {{- $term = trim $term " " }}
    {{- if not $term }}
      {{- continue }}
    {{- end }}
    {{- $term_key = ($term | plainify | anchorize) }}
    {{- with $page.Site.GetPage (printf "%s/%s" $taxonomy $term_key) }}
      {{- $term_pages = $term_pages | append (dict "Title" (default (humanize .Data.Term | strings.Title) .Title) "Term" . )}}
    {{- else }}
      {{- $term_key = ($term | urlize) }}
      {{- with $page.Site.GetPage (printf "%s/%s" $taxonomy $term_key) }}
        {{- $term_pages = $term_pages | append (dict "Title" (default (humanize .Data.Term | strings.Title) .Title) "Term" . )}}
      {{- end }}
    {{- end }}
  {{- end }}
{{- end }}
{{- $taxonomy_title := default (default $taxonomy_page.Data.Plural (i18n $taxonomy_page.Data.Plural)) $taxonomy_page.Params.Title }}
{{- with $term_pages }}
<div class=" taxonomy-{{ $taxonomy }} term-list cstyle {{ $style }} {{ $class }}" title="{{ $taxonomy_title }}"{{ if $color }}{{ printf " style=\"--VARIABLE-TAGS-BG-color: %s;\"" $color | safeHTMLAttr }}{{ end }}>
  {{- if $icon }}
  <i class="{{ $icon }}"></i>
  {{- end }}
  <ul>
  {{- range sort . ".Title" }}
    {{- $to := .Term }}
    <li><a class="term-link" href="{{ partial "relLangPrettyUglyURL.hugo" (dict "to" $to) }}">{{ .Title }}</a></li>
  {{- end }}
  </ul>
</div>
{{- end }}