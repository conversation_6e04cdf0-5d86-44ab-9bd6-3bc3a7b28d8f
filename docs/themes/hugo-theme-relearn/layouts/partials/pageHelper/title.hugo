{{- $title := "" }}
{{- with .page }}
  {{- if .Params.menuTitle }}
    {{- warnf "%q: UNSUPPORTED frontmatter 'menutitle' found, use 'linktitle' instead" $.File.Filename }}
  {{- end }}
  {{- $format := $.format | default (partial "get-format.hugo" .) }}
  {{- $outputFormat := $.outputFormat | default (partial "output-format.hugo" (dict "page" . "format" $format)) }}
  {{- $title = .Title }}
  {{- if $.linkTitle }}
    {{- $title = or $.page.LinkTitle $title }}
  {{- end }}

  {{- if eq $outputFormat "searchpage" }}
    {{- $title = T "Search" }}
  {{- else if eq .Kind "home" }}
    {{- $title = or $title site.Title }}
  {{- else if eq .Kind "taxonomy" }}
    {{- $title = default (default .Data.Plural (i18n .Data.Plural)) .Params.Title }}
  {{- else if eq .Kind "term" }}
    {{- $taxonomy_page := .Site.GetPage .Data.Plural }}
    {{- $taxonomy_title := default (default $taxonomy_page.Data.Singular (i18n $taxonomy_page.Data.Singular)) $taxonomy_page.Params.SingularTitle }}
    {{- $term_title := default (humanize .Data.Term | strings.Title) .Title }}
    {{- if $.linkTitle }}
      {{- $title = printf "%s" $term_title }}
    {{- else if $.reverse }}
      {{- $title = printf "%s %s %s" $term_title (default "::" .Site.Params.titleSeparator) $taxonomy_title }}
    {{- else }}
      {{- $title = printf "%s %s %s" $taxonomy_title (default "::" .Site.Params.titleSeparator) $term_title }}
    {{- end }}
  {{- end }}

  {{- if $.fullyQualified }}
    {{- if and $title site.Title (not (eq $title site.Title)) }}
      {{- if $.reverse }}
        {{- $title = printf "%s %s %s" $title (default "::" site.Params.titleSeparator) site.Title }}
      {{- else }}
        {{- $title = printf "%s %s %s" site.Title (default "::" site.Params.titleSeparator) $title }}
      {{- end }}
    {{- end }}
  {{- end }}
{{- end }}
{{- return $title }}