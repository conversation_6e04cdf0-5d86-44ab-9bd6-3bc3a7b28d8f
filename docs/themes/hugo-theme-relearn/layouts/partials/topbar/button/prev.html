{{- $onwidths := cond (isset . "onwidths") .onwidths "show" }}
{{- $onwidthm := cond (isset . "onwidthm") .onwidthm "show" }}
{{- $onwidthl := cond (isset . "onwidthl") .onwidthl "show" }}
{{- with .page }}
	{{- $format := partial "get-format.hugo" . }}
	{{- $outputFormat := partial "output-format.hugo" (dict "page" . "format" $format) }}
	{{- $showPrevNext := (and (not .Params.disableNextPrev) (not .Site.Params.disableNextPrev)) }}
	{{- if $showPrevNext }}
		{{- $startarrow := "🡐" }}
		{{- if eq (.Language.LanguageDirection | default (T "Reading-direction") | default "ltr") "rtl" }}
			{{- $startarrow = "🡒" }}
		{{- end }}
		{{- $prev := .Scratch.Get "relearnPrevPage" | default .Site.Home }}
		{{- if eq $outputFormat "searchpage" }}
			{{- $outputFormat = "html" }}
		{{- else if eq .Page.Kind "home" }}
			{{- $prev = "" }}
		{{- else if eq .Page.Kind "term" }}
			{{- /* go to previous term page or taxonomy page if it is the first term */}}
			{{- $taxonomy_page := .Site.GetPage .Data.Plural }}
			{{- $pages := partialCached "partials/pageHelper/taxonomyPages.html" $taxonomy_page $taxonomy_page }}
			{{- $prev = partial "partials/pageHelper/prev.html" (dict "collection" $pages "item" .) | default $taxonomy_page }}
		{{- end }}
		{{- $prevTitle := partial "pageHelper/title.hugo" (dict "page" $prev "format" $format "outputFormat" $outputFormat) }}
		{{- partial "topbar/func/button.html" (dict
			"page" .
			"class" "topbar-button-prev"
			"href" (partial "relLangPrettyUglyURL.hugo" (dict "to" $prev))
			"icon" "chevron-left"
			"onwidths" $onwidths
			"onwidthm" $onwidthm
			"onwidthl" $onwidthl
			"hint" (printf "%s (%s)" $prevTitle ($startarrow | safeHTML))
		)}}
	{{- end }}
{{- end }}