/* based on base16-snazzy */
/* Background */ .bg { color: #e2e4e5; background-color: #282a36; }
/* PreWrapper */ .chroma { color: #e2e4e5; background-color: #282a36; }
/* Other */ .chroma .x {  }
/* Error */ .chroma .err { color: #ff5c57 }
/* CodeLine */ .chroma .cl {  }
/* LineLink */ .chroma .lnlinks { outline: none; text-decoration: none; color: inherit }
/* LineTableTD */ .chroma .lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .chroma .lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; }
/* LineHighlight */ .chroma .hl { background-color: #505050 }
/* LineNumbersTable */ .chroma .lnt { white-space: pre; -webkit-user-select: none; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* LineNumbers */ .chroma .ln { white-space: pre; -webkit-user-select: none; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* Line */ .chroma .line { display: flex; }
/* Keyword */ .chroma .k { color: #ff6ac1 }
/* KeywordConstant */ .chroma .kc { color: #ff6ac1 }
/* KeywordDeclaration */ .chroma .kd { color: #ff5c57 }
/* KeywordNamespace */ .chroma .kn { color: #ff6ac1 }
/* KeywordPseudo */ .chroma .kp { color: #ff6ac1 }
/* KeywordReserved */ .chroma .kr { color: #ff6ac1 }
/* KeywordType */ .chroma .kt { color: #9aedfe }
/* Name */ .chroma .n {  }
/* NameAttribute */ .chroma .na { color: #57c7ff }
/* NameBuiltin */ .chroma .nb { color: #ff5c57 }
/* NameBuiltinPseudo */ .chroma .bp {  }
/* NameClass */ .chroma .nc { color: #f3f99d }
/* NameConstant */ .chroma .no { color: #ff9f43 }
/* NameDecorator */ .chroma .nd { color: #ff9f43 }
/* NameEntity */ .chroma .ni {  }
/* NameException */ .chroma .ne {  }
/* NameFunction */ .chroma .nf { color: #57c7ff }
/* NameFunctionMagic */ .chroma .fm {  }
/* NameLabel */ .chroma .nl { color: #ff5c57 }
/* NameNamespace */ .chroma .nn {  }
/* NameOther */ .chroma .nx {  }
/* NameProperty */ .chroma .py {  }
/* NameTag */ .chroma .nt { color: #ff6ac1 }
/* NameVariable */ .chroma .nv { color: #ff5c57 }
/* NameVariableClass */ .chroma .vc { color: #ff5c57 }
/* NameVariableGlobal */ .chroma .vg { color: #ff5c57 }
/* NameVariableInstance */ .chroma .vi { color: #ff5c57 }
/* NameVariableMagic */ .chroma .vm {  }
/* Literal */ .chroma .l {  }
/* LiteralDate */ .chroma .ld {  }
/* LiteralString */ .chroma .s { color: #5af78e }
/* LiteralStringAffix */ .chroma .sa { color: #5af78e }
/* LiteralStringBacktick */ .chroma .sb { color: #5af78e }
/* LiteralStringChar */ .chroma .sc { color: #5af78e }
/* LiteralStringDelimiter */ .chroma .dl { color: #5af78e }
/* LiteralStringDoc */ .chroma .sd { color: #5af78e }
/* LiteralStringDouble */ .chroma .s2 { color: #5af78e }
/* LiteralStringEscape */ .chroma .se { color: #5af78e }
/* LiteralStringHeredoc */ .chroma .sh { color: #5af78e }
/* LiteralStringInterpol */ .chroma .si { color: #5af78e }
/* LiteralStringOther */ .chroma .sx { color: #5af78e }
/* LiteralStringRegex */ .chroma .sr { color: #5af78e }
/* LiteralStringSingle */ .chroma .s1 { color: #5af78e }
/* LiteralStringSymbol */ .chroma .ss { color: #5af78e }
/* LiteralNumber */ .chroma .m { color: #ff9f43 }
/* LiteralNumberBin */ .chroma .mb { color: #ff9f43 }
/* LiteralNumberFloat */ .chroma .mf { color: #ff9f43 }
/* LiteralNumberHex */ .chroma .mh { color: #ff9f43 }
/* LiteralNumberInteger */ .chroma .mi { color: #ff9f43 }
/* LiteralNumberIntegerLong */ .chroma .il { color: #ff9f43 }
/* LiteralNumberOct */ .chroma .mo { color: #ff9f43 }
/* Operator */ .chroma .o { color: #ff6ac1 }
/* OperatorWord */ .chroma .ow { color: #ff6ac1 }
/* Punctuation */ .chroma .p {  }
/* Comment */ .chroma .c { color: #78787e }
/* CommentHashbang */ .chroma .ch { color: #78787e }
/* CommentMultiline */ .chroma .cm { color: #78787e }
/* CommentSingle */ .chroma .c1 { color: #78787e }
/* CommentSpecial */ .chroma .cs { color: #78787e }
/* CommentPreproc */ .chroma .cp { color: #78787e }
/* CommentPreprocFile */ .chroma .cpf { color: #78787e }
/* Generic */ .chroma .g {  }
/* GenericDeleted */ .chroma .gd { color: #ff5c57 }
/* GenericEmph */ .chroma .ge { text-decoration: underline }
/* GenericError */ .chroma .gr { color: #ff5c57 }
/* GenericHeading */ .chroma .gh { font-weight: bold }
/* GenericInserted */ .chroma .gi { font-weight: bold }
/* GenericOutput */ .chroma .go { color: #43454f }
/* GenericPrompt */ .chroma .gp {  }
/* GenericStrong */ .chroma .gs { font-style: italic }
/* GenericSubheading */ .chroma .gu { font-weight: bold }
/* GenericTraceback */ .chroma .gt {  }
/* GenericUnderline */ .chroma .gl { text-decoration: underline }
/* TextWhitespace */ .chroma .w {  }
