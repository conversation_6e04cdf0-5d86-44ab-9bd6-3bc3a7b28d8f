:root {
    --PRIMARY-color: rgba( 131, 201, 50, 1 ); /* brand primary color */
    --SECONDARY-color: rgba( 99, 128, 208, 1 ); /* brand secondary color */
    --ACCENT-color: rgb( 255, 102, 78, 1 ); /* brand accent color, used for search highlights */

    --MAIN-TEXT-color: rgba( 0, 0, 0, 1 ); /* text color of content and h1 titles */
    --MAIN-LINK-HOVER-color: rgba( 32, 40, 145, 1 ); /* hovered link color of content */
    --MAIN-BG-color: rgba( 255, 255, 255, 1 ); /* background color of content */
    --MAIN-TITLES-TEXT-color: rgba( 16, 16, 16, 1 ); /* text color of h2-h6 titles and transparent box titles */

    --CODE-theme: relearn-light; /* name of the chroma stylesheet file */
    --CODE-BLOCK-color: rgba( 39, 40, 34, 1 ); /* fallback text color of block code; should be adjusted to your selected chroma style */
    --CODE-BLOCK-BG-color: rgba( 250, 250, 250, 1 ); /* fallback background color of block code; should be adjusted to your selected chroma style */
    --CODE-BLOCK-BORDER-color: rgba( 216, 216, 216, 1 ); /* border color of block code */
    --CODE-INLINE-color: rgba( 94, 94, 94, 1 ); /* text color of inline code */
    --CODE-INLINE-BG-color: rgba( 255, 250, 233, 1 ); /* background color of inline code */
    --CODE-INLINE-BORDER-color: rgba( 248, 232, 200, 1 ); /* border color of inline code */

    --BROWSER-theme: light; /* name of the theme for browser scrollbars of the main section */
    --MERMAID-theme: default; /* name of the default Mermaid theme for this variant, can be overridden in hugo.toml */
    --OPENAPI-theme: light; /* name of the default OpenAPI theme for this variant, can be overridden in hugo.toml */
    --OPENAPI-CODE-theme: idea; /* name of the default OpenAPI code theme for this variant, can be overridden in hugo.toml */

    --MENU-HEADER-BG-color: rgba( 0, 0, 0, 0 ); /* background color of menu header */
    --MENU-HEADER-SEPARATOR-color: rgba( 96, 96, 96, 1 ); /* separator color between menu header and menu */

    --MENU-HOME-LINK-color: rgba( 64, 64, 64, 1 ); /* home button color if configured */
    --MENU-HOME-LINK-HOVER-color: rgba( 0, 0, 0, 1 ); /* hovered home button color if configured */

    --MENU-SEARCH-color: rgba( 64, 64, 64, 1 ); /* text and icon color of search box */
    --MENU-SEARCH-BG-color: rgba( 255, 255, 255, .2 ); /* background color of search box */
    --MENU-SEARCH-BORDER-color: transparent; /* border color of search box */

    --MENU-SECTIONS-BG-color: rgba( 131, 201, 50, 1 ); /* background of the menu; this is NOT just a color value but can be a complete CSS background definition including gradients, etc. */
    --MENU-SECTIONS-ACTIVE-BG-color: transparent; /* background color of the active menu section */
    --MENU-SECTIONS-LINK-color: rgba( 50, 50, 50, 1 ); /* link color of menu topics */
    --MENU-SECTIONS-LINK-HOVER-color: rgba( 255, 255, 255, 1 ); /* hovered link color of menu topics */
    --MENU-SECTION-ACTIVE-CATEGORY-color: rgba( 50, 50, 50, 1 ); /* text color of the displayed menu topic */
    --MENU-SECTION-SEPARATOR-color: rgba( 96, 96, 96, 1 ); /* separator color between menu sections and menu footer */

    --BOX-CAPTION-color: rgba( 255, 255, 255, 1 ); /* text color of colored box titles */
    --BOX-BG-color: rgba( 255, 255, 255, .833 ); /* background color of colored boxes */
    --BOX-TEXT-color: rgba( 16, 16, 16, 1 ); /* text color of colored box content */
}

body a#R-logo,
body a#R-logo:hover,
body #R-logo svg,
body #R-logo svg * {
    color: var(--MENU-SEARCH-color);
    fill: var(--MENU-SEARCH-color) !important;
}
