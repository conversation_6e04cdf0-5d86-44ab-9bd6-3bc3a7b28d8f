name: docs-build

on:
  push: # Build on all pushes but only deploy for main branch
  pull_request: # Build on all PRs regardless what branch
  workflow_dispatch: # Allow this task to be manually started (you'll never know)

jobs:
  ci:
    name: Run build
    runs-on: ubuntu-latest
    if: github.event_name != 'push' || (github.event_name == 'push' && github.ref != 'refs/heads/main')
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          submodules: true  # Fetch Hugo themes (true OR recursive)
          # fetch-depth: 0   # Fetch all history for .GitInfo and .Lastmod - not necessary for this repo

      - name: Build site
        uses: ./.github/actions/build_site
