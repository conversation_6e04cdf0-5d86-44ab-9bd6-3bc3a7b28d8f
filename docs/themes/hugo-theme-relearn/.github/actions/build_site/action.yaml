name: Build site
description: Builds the Hugo exampleSite for later deploy on GitHub-Pages
runs:
  using: composite
  steps:
    - name: Setup Hugo
      uses: peaceiris/actions-hugo@v3
      with:
        hugo-version: 'latest'

    - name: Build site
      shell: bash
      run: |
        hugo --source ${GITHUB_WORKSPACE}/exampleSite --destination ${GITHUB_WORKSPACE}/../public --cleanDestinationDir --environment github --theme ${GITHUB_WORKSPACE}
