name: Deploy site
description: Deploys a built site on GitHub-Pages
inputs:
  github_token:
    description: Secret GitHub token
    required: true
runs:
  using: composite
  steps:
    - name: Deploy site
      uses: peaceiris/actions-gh-pages@v3
      env:
        GITHUB_TOKEN: ${{ inputs.github_token }}
        GITHUB_WORKSPACE: ${{ github.workspace }}
      with:
        github_token: ${{ env.GITHUB_TOKEN }}
        publish_dir: ${{ env.GITHUB_WORKSPACE }}/../public
