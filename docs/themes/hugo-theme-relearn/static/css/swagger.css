/* Styles to make Swagger-UI fit into our theme */

@import "fonts.css";
@import "variant-internal.css";

body{
    line-height: 1.574;
    font-weight: 300;
    margin: 0;
    overflow: hidden;
}
body,
.swagger-ui .info *,
#relearn-swagger-ui .renderedMarkdown *,
#relearn-swagger-ui p{
    font-size: 1.015625rem;
}
.swagger-ui .scheme-container{
    padding-left: 1rem;
    padding-right: 1rem;
}
.swagger-ui .wrapper{
    padding-left: 0;
    padding-right: 0;
}
h2 {
    font-weight: 500;
}
svg{
    fill: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .info h2.title {
    color: var(--INTERNAL-MAIN-TITLES-H2-TEXT-color);
}
.relearn-expander{
    display: block;
    float: right;
    margin: .5rem;
}
#relearn-swagger-ui{
    clear: both;
}

/* Styles extracted from swagger-dark.css generated by Dark Reader */

html {
    background-color: var(--INTERNAL-MAIN-BG-color) !important;
}
html {
    color-scheme: var(--INTERNAL-BROWSER-theme) !important;
}
html, body {
    background-color: var(--INTERNAL-MAIN-BG-color);
}
html, body {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
a {
    color: var(--INTERNAL-MAIN-LINK-color);
}
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
    color: var(--INTERNAL-MAIN-TEXT-color) !important;
}
::-webkit-scrollbar-corner {
    background-color: var(--INTERNAL-MAIN-BG-color);
}
::selection {
    color: var(--INTERNAL-MAIN-TEXT-color) !important;
}
::-moz-selection {
    color: var(--INTERNAL-MAIN-TEXT-color) !important;
}
*:not(pre, pre *, code, .far, .fa, .glyphicon, [class*="vjs-"], .fab, .fa-github, .fas, .material-icons, .icofont, .typcn, mu, [class*="mu-"], .glyphicon, .icon) {
  font-family: var(--INTERNAL-MAIN-font) !important;
}
:root {
   --darkreader-neutral-background: var(--INTERNAL-MAIN-BG-color);
   --darkreader-neutral-text: var(--INTERNAL-MAIN-TEXT-color);
   --darkreader-selection-text: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .nested-links a {
    color: var(--INTERNAL-MAIN-LINK-color);
}
.swagger-ui .nested-links a:focus,
.swagger-ui .nested-links a:hover {
    color: var(--INTERNAL-MAIN-LINK-HOVER-color);
}
.swagger-ui .opblock-tag {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .opblock-tag small {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .parameter__type {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .opblock .opblock-section-header > label {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .opblock .opblock-section-header h4 {
    color: var(--INTERNAL-MAIN-TITLES-H4-TEXT-color);
}
.swagger-ui .opblock .opblock-summary-operation-id,
.swagger-ui .opblock .opblock-summary-path,
.swagger-ui .opblock .opblock-summary-path__deprecated {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .opblock .opblock-summary-description {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .opblock.opblock-post {
    border-color: var(--INTERNAL-BOX-GREEN-color);
}
.swagger-ui .opblock.opblock-post .opblock-summary-method {
    background-color: var(--INTERNAL-BOX-GREEN-color);
}
.swagger-ui .opblock.opblock-post .opblock-summary {
    border-color: var(--INTERNAL-BOX-GREEN-color);
}
.swagger-ui .opblock.opblock-post .tab-header .tab-item.active h4 span::after {
    background-color: var(--INTERNAL-BOX-GREEN-color);
}
.swagger-ui .opblock.opblock-put {
    border-color: var(--INTERNAL-BOX-ORANGE-color);
}
.swagger-ui .opblock.opblock-put .opblock-summary-method {
    background-color: var(--INTERNAL-BOX-ORANGE-color);
}
.swagger-ui .opblock.opblock-put .opblock-summary {
    border-color: var(--INTERNAL-BOX-ORANGE-color);
}
.swagger-ui .opblock.opblock-put .tab-header .tab-item.active h4 span::after {
    background-color: var(--INTERNAL-BOX-ORANGE-color);
}
.swagger-ui .opblock.opblock-delete {
    border-color: var(--INTERNAL-BOX-RED-color);
}
.swagger-ui .opblock.opblock-delete .opblock-summary-method {
    background-color: var(--INTERNAL-BOX-RED-color);
}
.swagger-ui .opblock.opblock-delete .opblock-summary {
    border-color: var(--INTERNAL-BOX-RED-color);
}
.swagger-ui .opblock.opblock-delete .tab-header .tab-item.active h4 span::after {
    background-color: var(--INTERNAL-BOX-RED-color);
}
.swagger-ui .opblock.opblock-get {
    border-color: var(--INTERNAL-BOX-BLUE-color);
}
.swagger-ui .opblock.opblock-get .opblock-summary-method {
    background-color: var(--INTERNAL-BOX-BLUE-color);
}
.swagger-ui .opblock.opblock-get .opblock-summary {
    border-color: var(--INTERNAL-BOX-BLUE-color);
}
.swagger-ui .opblock.opblock-get .tab-header .tab-item.active h4 span::after {
    background-color: var(--INTERNAL-BOX-BLUE-color);
}
.swagger-ui .tab li {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .opblock-description-wrapper,
.swagger-ui .opblock-external-docs-wrapper,
.swagger-ui .opblock-title_normal {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .opblock-description-wrapper h4,
.swagger-ui .opblock-external-docs-wrapper h4,
.swagger-ui .opblock-title_normal h4 {
    color: var(--INTERNAL-MAIN-TITLES-H4-TEXT-color);
}
.swagger-ui .opblock-description-wrapper p,
.swagger-ui .opblock-external-docs-wrapper p,
.swagger-ui .opblock-title_normal p {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .responses-inner h4{
    color: var(--INTERNAL-MAIN-TITLES-H4-TEXT-color);
}
.swagger-ui .responses-inner h5 {
    color: var(--INTERNAL-MAIN-TITLES-H5-TEXT-color);
}
.swagger-ui .response-col_status {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .response-col_links {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .opblock-body pre.microlight {
    background-color: var(--INTERNAL-MAIN-BG-color);
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .scheme-container .schemes > label {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .loading-container .loading::after {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui section h3 {
    color: var(--INTERNAL-MAIN-TITLES-H3-TEXT-color);
}
.swagger-ui .btn {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui select {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui label {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui textarea {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .checkbox p {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .dialog-ux .modal-ux-content p {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .dialog-ux .modal-ux-content h4 {
    color: var(--INTERNAL-MAIN-TITLES-H4-TEXT-color);
}
.swagger-ui .dialog-ux .modal-ux-header h3 {
    color: var(--INTERNAL-MAIN-TITLES-H3-TEXT-color);
}
.swagger-ui .model {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui section.models h4 {
    color: var(--INTERNAL-MAIN-TITLES-H4-TEXT-color);
}
.swagger-ui section.models h5 {
    color: var(--INTERNAL-MAIN-TITLES-H5-TEXT-color);
}
.swagger-ui .model-title {
    color: var(--INTERNAL-MAIN-TITLES-TEXT-color);
}
.swagger-ui .prop-format {
    color: var(--INTERNAL-MAIN-TITLES-TEXT-color);
}
.swagger-ui .servers > label {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui table.headers td {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui table thead tr td,
.swagger-ui table thead tr th {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .parameter__name {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .info li,
.swagger-ui .info p,
.swagger-ui .info table {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .info h1 {
    color: var(--INTERNAL-MAIN-TITLES-H1-TEXT-color);
}
.swagger-ui .info h2 {
    color: var(--INTERNAL-MAIN-TITLES-H2-TEXT-color);
}
.swagger-ui .info h3 {
    color: var(--INTERNAL-MAIN-TITLES-H3-TEXT-color);
}
.swagger-ui .info h4 {
    color: var(--INTERNAL-MAIN-TITLES-H4-TEXT-color);
}
.swagger-ui .info h5 {
    color: var(--INTERNAL-MAIN-TITLES-H5-TEXT-color);
}
.swagger-ui .info a {
    color: var(--INTERNAL-MAIN-LINK-color);
}
.swagger-ui .info a:hover {
    color: var(--INTERNAL-MAIN-LINK-HOVER-color);
}
.swagger-ui .info .base-url {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .info .title {
    color: var(--INTERNAL-MAIN-TITLES-TEXT-color);
}
.swagger-ui .auth-container .errors {
    color: var(--INTERNAL-MAIN-TEXT-color);
}
.swagger-ui .scopes h2 {
    color: var(--INTERNAL-MAIN-TITLES-H2-TEXT-color);
}
.swagger-ui .scopes h2 a {
    color: var(--INTERNAL-MAIN-LINK-color);
}
.swagger-ui .errors-wrapper .errors h4 {
    color: var(--INTERNAL-MAIN-TITLES-H4-TEXT-color);
}
.swagger-ui .errors-wrapper .errors small {
    color: var(--INTERNAL-MAIN-TITLES-TEXT-color);
}
.swagger-ui .errors-wrapper hgroup h4 {
    color: var(--INTERNAL-MAIN-TITLES-H4-TEXT-color);
}
body {
    background-color: var(--INTERNAL-MAIN-BG-color);
}
