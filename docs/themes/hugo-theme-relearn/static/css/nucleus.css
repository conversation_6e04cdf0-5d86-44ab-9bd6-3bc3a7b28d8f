*, *::before, *::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

@-webkit-viewport {
  width: device-width; }
@-moz-viewport {
  width: device-width; }
@-o-viewport {
  width: device-width; }
@viewport {
  width: device-width; }
html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%; }

body {
  margin: 0; }

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
  display: block; }

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline; }

audio:not([controls]) {
  display: none;
  height: 0; }

[hidden],
template {
  display: none; }

a {
  background: transparent;
  text-decoration: none; }

abbr[title] {
  border-bottom: 1px dotted; }

b,
strong {
  font-weight: bold; }

dfn {
  font-style: italic; }

sub,
sup {
  font-size: 0.8rem;
  line-height: 0;
  position: relative;
  vertical-align: baseline; }

sup {
  top: -0.5em; }

sub {
  bottom: -0.25em; }

iframe {
  border: 0; }

img {
  border: 0;
  max-width: 100%; }

svg:not(:root) {
  overflow: hidden; }

figure {
  margin: 1rem 2.5rem; }

hr {
  height: 0; }

pre:not(.mermaid) {
  overflow: auto; }

button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0; }

button {
  overflow: visible;
  padding: 0; }

button,
select {
  text-transform: none; }

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer; }

button[disabled],
html input[disabled] {
  cursor: default; }

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0; }

input {
  line-height: normal; }

input[type="checkbox"],
input[type="radio"] {
  padding: 0; }

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto; }

input[type="search"] {
  -webkit-appearance: textfield; }

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

legend {
  border: 0;
  padding: 0; }

textarea {
  overflow: auto; }

optgroup {
  font-weight: bold; }

table {
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
  width: 100%; }

tr, td, th {
  vertical-align: middle; }

th, td {
  padding: 0.425rem 0; }

th {
  text-align: start; }

p {
  margin: 1rem 0; }

ul, ol {
  margin-top: 1rem;
  margin-bottom: 1rem; }
  ul ul, ul ol, ol ul, ol ol {
    margin-top: 0;
    margin-bottom: 0; }

blockquote {
  margin: 1.5rem 0;
  padding-inline-start: 0.85rem; }

cite {
  display: block;
  font-size: 0.925rem; }
  cite:before {
    content: "\2014 \0020"; }

pre:not(.mermaid) {
  margin: 1.5rem 0;
  padding: 0.938rem; }

code {
  vertical-align: bottom; }

small {
  font-size: 0.925rem; }

hr {
  border-left: none;
  border-right: none;
  border-top: none;
  margin: 1.5rem 0; }

fieldset {
  border: 0;
  padding: 0.938rem;
  margin: 0 0 1rem 0; }

input,
label,
select {
  display: block; }

label {
  margin-bottom: 0.425rem; }
  label.required:after {
    content: "*"; }
  label abbr {
    display: none; }

textarea, input[type="email"], input[type="number"], input[type="password"], input[type="search"], input[type="tel"], input[type="text"], input[type="url"], input[type="color"], input[type="date"], input[type="datetime"], input[type="datetime-local"], input[type="month"], input[type="time"], input[type="week"], select[multiple=multiple] {
  -webkit-transition: border-color;
  -moz-transition: border-color;
  transition: border-color;
  border-radius: 0.1875rem;
  margin-bottom: 0.85rem;
  padding: 0.425rem 0.425rem;
  width: 100%; }
  textarea:focus, input[type="email"]:focus, input[type="number"]:focus, input[type="password"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="text"]:focus, input[type="url"]:focus, input[type="color"]:focus, input[type="date"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="month"]:focus, input[type="time"]:focus, input[type="week"]:focus, select[multiple=multiple]:focus {
    outline: none; }

textarea {
  resize: vertical; }

input[type="checkbox"], input[type="radio"] {
  display: inline;
  margin-inline-end: 0.425rem; }

input[type="file"] {
  width: 100%; }

select {
  width: auto;
  max-width: 100%;
  margin-bottom: 1rem; }

button,
input[type="submit"] {
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  white-space: nowrap;
  border: inherit; }
