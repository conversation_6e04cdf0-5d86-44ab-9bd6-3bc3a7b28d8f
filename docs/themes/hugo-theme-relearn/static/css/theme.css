@charset "UTF-8";

@import "tags.css";

/* until browsers don't let us set length values based on dppx, we
need a way to calculate them ourself */
:root {
    --dpr: 1;
    --bpx: 1;
    --bpx1: 1;
}
@media (min-resolution: 105dpi) {
    :root {
        --dpr: 1.1;
        --bpx: 1.1;
        --bpx1: calc(1/1.1);
    }
}
@media (min-resolution: 115dpi) {
    :root {
        --dpr: 1.2;
        --bpx: 1.2;
        --bpx1: calc(1/1.2);
    }
}
@media (min-resolution: 120dpi) {
    :root {
        --dpr: 1.25;
        --bpx: 1.25;
        --bpx1: calc(1/1.25);
    }
}
@media (min-resolution: 128dpi) {
    :root {
        --dpr: 1.333;
        --bpx: 1.333;
        --bpx1: calc(1/1.333);
    }
}
@media (min-resolution: 144dpi) {
    :root {
        --dpr: 1.5;
        --bpx: 1.5;
        --bpx1: calc(1/1.5);
    }
}
@media (min-resolution: 160dpi) {
    :root {
        --dpr: 1.666;
        --bpx: 1.666;
        --bpx1: calc(1/1.666);
    }
}
@media (min-resolution: 168dpi) {
    :root {
        --dpr: 1.75;
        --bpx: 1.75;
        --bpx1: calc(1/1.75);
    }
}
@media (min-resolution: 192dpi) {
    :root {
        --dpr: 2;
        --bpx: 1;
        --bpx1: 1;
    }
}
@media (min-resolution: 240dpi) {
    :root {
        --dpr: 2.5;
        --bpx: 1.25;
        --bpx1: calc(1/1.25);
    }
}
@media (min-resolution: 288dpi) {
    :root {
        --dpr: 3;
        --bpx: 1;
        --bpx1: 1;
    }
}
@media (min-resolution: 384dpi) {
    :root {
        --dpr: 4;
        --bpx: 1;
        --bpx1: 1;
    }
}
@media (min-resolution: 480dpi) {
    :root {
        --dpr: 5;
        --bpx: 1.25;
        --bpx1: calc(1/1.25);
    }
}
@media (min-resolution: 576dpi) {
    :root {
        --dpr: 6;
        --bpx: 1.5;
        --bpx1: calc(1/1.5);
    }
}
@media (min-resolution: 768dpi) {
    :root {
        --dpr: 8;
        --bpx: 1;
        --bpx1: 1;
    }
}

html {
    height: 100%;
    width: 100%;
}

body {
    display: flex;
    flex-direction: row-reverse; /* to allow body to have initial focus for PS in some browsers and better SEO and a11y */
    font-size: 1.015625rem;
    font-weight: 300;
    height: 100%;
    justify-content: flex-end;
    line-height: 1.574;
    /* overflow: hidden; PSC removed for #242 #243 #244; to avoid browser scrollbar to flicker before we create our own */
    width: 100%;
}

b,
strong,
label,
th {
    font-weight: 600;
}

ul {
    list-style: disc;
}

dt {
    font-style: italic;
}

dd {
    display: list-item;
    list-style: circle;
}

.default-animation{
    transition: all 0.35s ease;
}

#R-sidebar {
    display: flex;
    flex-basis: auto;
    flex-direction: column;
    flex-grow: 0;
    flex-shrink: 0;
    font-size: .953125rem;
    height: 100%;
    inset-inline-start: 0;
    line-height: 1.574;
    min-height: 100%;
    position: fixed;
    min-width: var(--INTERNAL-MENU-WIDTH-L);
    max-width: var(--INTERNAL-MENU-WIDTH-L);
    width: var(--INTERNAL-MENU-WIDTH-L);
}

#R-sidebar a{
    text-decoration: none;
}

#R-header-wrapper {
    text-align: center;
    padding: 1rem;
    position: relative;
}
#R-header {
    border-block-end: 1px solid transparent;
    position: relative;
    z-index: 1;
}
#R-header a {
    display: inline-block;
}

.searchbox {
    border-radius: 4px;
    border-style: solid;
    border-width: 1px;
    position: relative;
    margin-top: 1rem;
}

.searchbox > :first-child {
    inset-inline-start: .5rem;
    position: absolute;
}

.searchbox > button {
    -webkit-appearance: none;
    appearance: none;
    background-color: transparent;
    border: 0;
    margin: 0;
    padding: 0;
    top: .25rem;
}

.searchbox > i {
    top: .45rem;
}

.searchbox > :last-child {
    inset-inline-end: .5rem;
    position: absolute;
}

#R-sidebar .searchbox > :first-child,
#R-sidebar .searchbox > :last-child{
    opacity: .65;
}

#R-sidebar .searchbox button:hover {
    opacity: 1;
}

.searchbox input {
    display: inline-block;
    width: 100%;
    height: 1.875rem;
    background: transparent;
    border: 0;
    padding-bottom: 0;
    padding-inline-end: 1.6rem;
    padding-inline-start: 1.8rem;
    padding-top: 0;
    margin: 0;
    font-weight: 300;
}

.searchbox input::placeholder {
    opacity: .45;
}

#R-content-wrapper {
    --ps-rail-hover-color: rgba( 176, 176, 176, .25 );
    display: flex;
    flex-direction: column;
    flex: 1; /* fill rest of vertical space */
    overflow: hidden;
    position: relative; /* PS */
    z-index: 100;
}

#R-sidebar .padding {
    padding: 0 1rem;
}

.footerFooter {
    font-size: .8125rem;
    padding-top: 2rem;
    padding-bottom: .75rem;
    text-align: center;
}

.footerFooter > * {
    margin: 0 auto;
}

#R-footer > hr:first-child {
    margin-top: 0;
}

/* increase specifity to override following #R-content-wrapper hr style */
#R-footer > hr:nth-child(n) {
    margin-left: 0;
    margin-right: 0;
}

#R-sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#R-sidebar ul li {
    padding: 0;
}

#R-sidebar ul li.visited + span {
    margin-inline-end: 1rem;
}

#R-sidebar ul li .read-icon {
    display: none;
    font-size: .8125rem;
    inset-inline-end: 1rem;
    margin: .25rem 0 0 0;
    min-width: 1rem;
    position: absolute;
}

#R-sidebar ul li.visited > a .read-icon {
    display: inline;
}

#R-sidebar .nav-title {
    font-size: 2rem;
    font-weight: 200;
    letter-spacing: -.02175em;
    line-height: 110%;
    margin: 1.2rem 0 .8rem 0;
    padding-inline-start: 1rem;
    text-rendering: optimizeLegibility;
    text-transform: uppercase;
}

#R-sidebar .footermargin {
    flex-grow: 1;
}

#R-content-wrapper hr {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    margin: 1.5rem 1rem 1rem 1rem;
}

#R-body {
    display: flex;
    flex-basis: 100%;
    flex-direction: column;
    flex-grow: 1;
    flex-shrink: 0;
    height: 100%;
    margin-inline-start: var(--INTERNAL-MENU-WIDTH-L);
    min-height: 100%;
    overflow-wrap: break-word; /* avoid x-scrolling of body if it is to large to fit */
    position: relative; /* PS */
    min-width: calc( 100% - var(--INTERNAL-MENU-WIDTH-L) );
    max-width: calc( 100% - var(--INTERNAL-MENU-WIDTH-L) );
    width: calc( 100% - var(--INTERNAL-MENU-WIDTH-L) );
    z-index: 70;
}

#R-body img,
#R-body figure > figcaption > h4,
#R-body figure > figcaption > p,
#R-body .video-container {
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding: 0;
    text-align: center;
}

#R-body img:not(.lightbox-image).left {
    margin-left: 0;
}

#R-body img:not(.lightbox-image).right {
    margin-right: 0;
}

#R-body img:not(.lightbox-image).border,
#R-body .video-container.border {
    background-clip: padding-box;
    border: 1px solid rgba( 134, 134, 134, .333 );
}

#R-body img:not(.lightbox-image).shadow,
#R-body .video-container.shadow {
    box-shadow: 0 10px 30px rgba( 176, 176, 176, .666 );
}

#R-body img:not(.lightbox-image).inline {
    display: inline;
    margin: 0;
    vertical-align: bottom;
}

#R-body figure > figcaption{
    margin: 0;
}

#R-body figure > figcaption > h4{
    font-size: 1.0rem;
    font-weight: 500;
    margin: 0;
}

#R-body figure > figcaption > p{
    font-size: .85rem;
    font-weight: 300;
    margin-top: .15rem;
}
#R-body figure > figcaption > h4 + p{
    margin-top: 0;
}

#R-body-inner {
    display: flex;
    flex: auto;
    flex-direction: column;
    overflow-y: auto;
    padding: 0 3.25rem 4rem 3.25rem;
    position: relative; /* PS */
}
@media screen and (max-width: 59.999rem) {
    #R-body-inner {
        padding: 0 2rem 1rem 2rem;
    }
}
@media screen and (max-width: 47.999rem) {
    #R-body-inner {
        padding: 0 1.25rem .375rem 1.25rem;
    }
}

#R-topbar a:hover:empty,
#R-topbar a:active:empty,
#R-topbar a:focus:empty,
#R-topbar a:hover :not(i),
#R-topbar a:active :not(i),
#R-topbar a:focus :not(i),
#R-topbar button:hover :not(i),
#R-topbar button:active :not(i),
#R-topbar button:focus :not(i),
#R-topbar .title:hover,
#R-topbar .title:active,
#R-topbar .title:focus,
.topbar-content a:hover,
.topbar-content a:active,
.topbar-content a:focus,
article a:hover,
article a:active,
article a:focus,
article a:hover .copy-to-clipboard,
article a:active .copy-to-clipboard,
article a:focus .copy-to-clipboard {
    text-decoration: underline;
}
.topbar-content a:hover,
.topbar-content a:active,
.topbar-content a:focus,
article a:hover,
article a:active,
article a:focus,
article a:hover .copy-to-clipboard,
article a:active .copy-to-clipboard,
article a:focus .copy-to-clipboard {
    outline: none;
}
article a:hover > img:only-child:empty,
article a:active > img:only-child:empty,
article a:focus > img:only-child:empty{
    outline: auto;
}

#R-body-inner:focus-visible{
    /* remove focus indicator for programatically set focus */
    outline: none;
}

#R-body h1 + hr {
    margin-bottom: 2rem;
    margin-top: -1rem;
}

#R-body .flex-block-wrapper {
    margin-left: auto;
    margin-right: auto;
    max-width: calc( var(--INTERNAL-MAIN-WIDTH-MAX) - var(--INTERNAL-MENU-WIDTH-L) - 2 * 3.25rem );
    width: 100%;
}
body:not(.print) #R-body .narrow .flex-block-wrapper {
    max-width: calc( var(--INTERNAL-MAIN-WIDTH-MAX) - var(--INTERNAL-MENU-WIDTH-L) - 2 * 9.75rem );
}
/* we limit width if we have large screens */
body.main-width-max #R-body .flex-block-wrapper {
    width: calc( var(--INTERNAL-MAIN-WIDTH-MAX) - var(--INTERNAL-MENU-WIDTH-L) - 2 * 3.25rem );
}
body.main-width-max:not(.print) #R-body .narrow .flex-block-wrapper {
    width: calc( var(--INTERNAL-MAIN-WIDTH-MAX) - var(--INTERNAL-MENU-WIDTH-L) - 2 * 9.75rem );
}

body:not(.print) #R-body-inner.narrow {
    padding: 0 9.75rem 2rem 9.75rem;
}
@media screen and (max-width: 59.999rem) {
    body:not(.print) #R-body-inner.narrow {
        padding: 0 6.5rem 1rem 6.5rem;
    }
}
@media screen and (max-width: 47.999rem) {
    body:not(.print) #R-body-inner.narrow {
        padding: 0 3.25rem .375rem 3.25rem;
    }
}

#R-body-inner div.article-subheading,
#R-body-inner .chapter.deprecated h3:first-of-type {
    font-weight: 200;
    margin-top: 0;
    text-align: center;
}
body:not(.print) #R-body-inner.narrow div.article-subheading{
    margin-top: 2rem;
}
@media screen and (max-width: 59.999rem) {
    body:not(.print) #R-body-inner.narrow div.article-subheading{
        margin-top: 1rem;
    }
}
@media screen and (max-width: 47.999rem) {
    body:not(.print) #R-body-inner.narrow div.article-subheading{
        margin-top: .375rem;
    }
}

body:not(.print) #R-body-inner.narrow p {
    font-size: 1.2rem;
    text-align: justify;
}

mark {
    background: transparent;
    border-radius: 0.8em 0.3rem;
    -webkit-box-decoration-break: clone;
    box-decoration-break: clone;
    color: rgba( 0, 0, 0, 1 );
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
    margin: 0 -0.4rem;
    padding: 0.1em 0.4rem;
}

kbd {
    background-color: rgba( 134, 134, 134, .166 );
    border-color: rgba( 134, 134, 134, .5 );
    border-radius: 0.25rem;
    border-style: solid;
    border-width: 1px;
    box-shadow: 0 .0625rem 0 .0625rem rgba( 134, 134, 134, .5 );
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
    font-size: 0.75rem;
    line-height: 1;
    min-width: 0.75rem;
    padding: .125rem .3125rem .125rem .3125rem;
    position: relative;
    text-align: center;
    top: -.125rem;
}

h1 {
    font-size: 3.25rem;
    font-weight: 200;
    margin: 0.85rem 0 1rem 0;
    /* big titles cause a horizontal scrollbar - fixing this by wrapping text */
    overflow-wrap: break-word;
    overflow-x: hidden;
    text-align: center;
    text-rendering: optimizeLegibility;
    text-transform: uppercase;
}

body:not(.print) #R-body-inner.narrow h1 {
    border-bottom: 4px solid rgba( 134, 134, 134, .125 );
    font-size: 3.5rem;
}
@media only screen and (min-width: 48rem) and (max-width: 59.999rem) {
    body:not(.print) #R-body-inner.narrow h1 {
        font-size: 2.8rem;
    }
}
@media only screen and (max-width: 47.999rem) {
    body:not(.print) #R-body-inner.narrow h1 {
        font-size: 2.5rem;
    }
}

h2 {
    font-size: 2.2rem;
    font-weight: 500;
}

h3, .article-subheading {
    font-size: 1.8rem;
    font-weight: 500;
}

h4 {
    font-size: 1.85rem;
    font-weight: 300;
}

h5 {
    font-size: 1.6rem;
    font-weight: 300;
}

h6 {
    font-size: 1.3rem;
    font-weight: 300;
}

h2, h3, .article-subheading, h4, h5, h6 {
    letter-spacing: -.0625rem;
    margin: 2rem 0 1rem 0;
    /* big titles cause a horizontal scrollbar - fixing this by wrapping text */
    overflow-wrap: break-word;
    overflow-x: hidden;
    text-rendering: optimizeLegibility;
}

h2, h3, h4, h5, h6 {
    /* leave space for anchor to avoid overflow */
    padding-inline-end: 2rem;
}

blockquote {
    border-inline-start: .6rem solid rgba( 134, 134, 134, .4 );
}

blockquote p {
    font-size: 1.06640625rem;
    font-style: italic;
    opacity: .75;
    text-align: justify;
}

blockquote cite {
    display: block;
    font-weight: bold;
    opacity: .5;
    padding-top: .5rem;
    text-align: end;
}

/* colored boxes */

.cstyle {
    --VARIABLE-BOX-color: var(--INTERNAL-BOX-NEUTRAL-color);
    --VARIABLE-BOX-CAPTION-color: var(--INTERNAL-BOX-CAPTION-color);
    --VARIABLE-BOX-BG-color: var(--INTERNAL-BOX-BG-color);
    --VARIABLE-BOX-TEXT-color: var(--INTERNAL-BOX-NEUTRAL-TEXT-color);
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
}

div.box {
    margin: 1.5rem 0;
    border-style: solid;
    border-width: 1px;
}

div.box > .box-label {
    font-weight: 500;
    padding: .2rem .6rem;
}

div.box > .box-content {
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
}

p:empty {
    /* in case of image render hook, Hugo may generate empty <p>s that we want to ignore */
    display: none;
}

/* in case of image render hook, Hugo may generate empty <p>s that we want to ignore as well, so a simple :first-child or :last-child is not enough */
#R-body table th > :nth-child(1 of :not(:empty)),
#R-body table th > :nth-child(1 of :not(:empty)) :nth-child(1 of :not(:empty)),
#R-body table td > :nth-child(1 of :not(:empty)),
#R-body table td > :nth-child(1 of :not(:empty)) :nth-child(1 of :not(:empty)),
#R-body div.box > .box-content > :nth-child(1 of :not(:empty)),
#R-body div.box > .box-content > :nth-child(1 of :not(:empty)) :nth-child(1 of :not(:empty)),
#R-body div.expand > .expand-content-text > :nth-child(1 of :not(:empty)),
#R-body div.expand > .expand-content-text > :nth-child(1 of :not(:empty)) :nth-child(1 of :not(:empty)),
#R-body div.tab-content > .tab-content-text > :nth-child(1 of :not(:empty)),
#R-body div.tab-content > .tab-content-text > :nth-child(1 of :not(:empty)) :nth-child(1 of :not(:empty)) {
    margin-top: 0;
}

#R-body table th > :nth-last-child(1 of :not(:empty)),
#R-body table th > :nth-last-child(1 of :not(:empty)) :nth-last-child(1 of :not(:empty)),
#R-body table th > div.highlight:last-child pre:not(.mermaid),
#R-body table td > :nth-last-child(1 of :not(:empty)),
#R-body table td > :nth-last-child(1 of :not(:empty)) :nth-last-child(1 of :not(:empty)),
#R-body table td > div:last-child pre:not(.mermaid),
#R-body div.box > .box-content > :nth-last-child(1 of :not(:empty)),
#R-body div.box > .box-content > :nth-last-child(1 of :not(:empty)) :nth-last-child(1 of :not(:empty)),
#R-body div.box > .box-content > div:last-child pre:not(.mermaid),
#R-body div.expand > .expand-content-text > :nth-last-child(1 of :not(:empty)),
#R-body div.expand > .expand-content-text > :nth-last-child(1 of :not(:empty)) :nth-last-child(1 of :not(:empty)),
#R-body div.expand > .expand-content-text > div:last-child pre:not(.mermaid),
#R-body div.tab-content > .tab-content-text > :nth-last-child(1 of :not(:empty)),
#R-body div.tab-content > .tab-content-text > :nth-last-child(1 of :not(:empty)) :nth-last-child(1 of :not(:empty)),
#R-body div.tab-content > .tab-content-text > div:last-child pre:not(.mermaid) {
    margin-bottom: 0;
}

/* resources shortcode */

div.attachments .box-content {
    display: block;
    margin: 0;
    padding-inline-start: 1.75rem;
}

/* Children shortcode */

.children p {
    font-size: .8125rem;
    margin-bottom:  0;
    margin-top: 0;
    padding-bottom: 0;
    padding-top: 0;
}

.children-li p {
    font-size: .8125rem;
    font-style: italic;
}

.children-h2 p,
.children-h3 p {
    font-size: .8125rem;
    margin-bottom:  0;
    margin-top: 0;
    padding-bottom: 0;
    padding-top: 0;
}

#R-body-inner .children h2,
#R-body-inner .children h3,
#R-body-inner .children h4,
#R-body-inner .children h5,
#R-body-inner .children h6 {
    margin-bottom: 0;
    margin-top: 1rem;
}
#R-body-inner ul.children-h2,
#R-body-inner ul.children-h3,
#R-body-inner ul.children-h4,
#R-body-inner ul.children-h5,
#R-body-inner ul.children-h6 {
    /* if we display children with style=h2 but without a containerstyle
    a ul will be used for structuring; we remove default indention for uls
    in this case */
    padding-inline-start: 0;
}

code,
kbd,
pre:not(.mermaid),
samp {
    font-size: .934375rem;
    vertical-align: baseline;
}

code {
    border-radius: 2px;
    border-style: solid;
    border-width: 1px;
    -webkit-print-color-adjust: economy;
    color-adjust: economy;
    padding-left: 2px;
    padding-right: 2px;
    white-space: nowrap;
}

span.copy-to-clipboard {
    display: inline-block;
    white-space: nowrap;
}

code.copy-to-clipboard-code {
    border-end-end-radius: 0;
    border-start-end-radius: 0;
    border-inline-end-width: 0;
}

pre:not(.mermaid) {
    border-radius: 2px;
    border-style: solid;
    border-width: 1px;
    -webkit-print-color-adjust: economy;
    color-adjust: economy;
    line-height: 1.15;
    padding: 1rem;
    position: relative;
}

/* pre:not(.mermaid):has( code ), */
/* the :has() operator isn't available in FF yet, so we patch this by JS */
pre:not(.mermaid).pre-code {
    direction: ltr;
    text-align: left;
}

pre:not(.mermaid) code {
    background-color: inherit;
    border: 0;
    color: inherit;
    -webkit-print-color-adjust: economy;
    color-adjust: economy;
    font-size: .9375rem;
    margin: 0;
    padding: 0;
}

div.highlight{
    position: relative;
}
/* we may have special treatment if highlight shortcode was used in table lineno mode */
div.highlight > div{
    border-style: solid;
    border-width: 1px;
}
/* remove default style for usual markdown tables */
div.highlight > div table{
    background-color: transparent;
    border-width: 0;
    margin: 0;
}
div.highlight > div td{
    border-width: 0;
}
#R-body div.highlight > div a {
    line-height: inherit;
}
#R-body div.highlight > div a:after {
    display: none;
}
/* disable selection for lineno cells */
div.highlight > div td:first-child:not(:last-child){
    -webkit-user-select: none;
    user-select: none;
}
/* increase code column to full width if highlight shortcode was used in table lineno mode  */
div.highlight > div td:not(:first-child):last-child{
    width: 100%;
}
/* add scrollbars if highlight shortcode was used in table lineno mode */
div.highlight > div table{
    display: block;
    overflow: auto;
}
div.highlight:not(.wrap-code) pre:not(.mermaid){
    overflow: auto;
}
div.highlight:not(.wrap-code) pre:not(.mermaid) code{
    white-space: pre;
}
div.highlight.wrap-code pre:not(.mermaid) code{
    white-space: pre-wrap;
}
/* remove border from row cells if highlight shortcode was used in table lineno mode */
div.highlight > div td > pre:not(.mermaid) {
    border-radius: 0;
    border-width: 0;
}
/* in case of table lineno mode we want to move each row closer together - besides the edges
this usually applies only to wrapfix tables but it doesn't hurt for non-wrapfix tables too */
div.highlight > div tr:not(:first-child) pre:not(.mermaid){
    padding-top: 0;
}
div.highlight > div tr:not(:last-child) pre:not(.mermaid){
    padding-bottom: 0;
}
/* in case of table lineno mode we want to move each columns closer together on the inside */
div.highlight > div td:first-child:not(:last-child) pre:not(.mermaid){
    padding-right: 0;
}
div.highlight > div td:not(:first-child):last-child pre:not(.mermaid){
    padding-left: 0;
}

hr {
    border-bottom: 4px solid rgba( 134, 134, 134, .125 );
}

#R-body-inner pre:not(.mermaid) {
    white-space: pre-wrap;
}

table {
    border: 1px solid rgba( 134, 134, 134, .333 );
    margin-bottom: 1rem;
    margin-top: 1rem;
    table-layout: auto;
}

th,
thead td {
    background-color: rgba( 134, 134, 134, .166 );
    border: 1px solid rgba( 134, 134, 134, .333 );
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
    padding: 0.5rem;
}

td {
    border: 1px solid rgba( 134, 134, 134, .333 );
    padding: 0.5rem;
}
tbody > tr:nth-child(even) > td {
    background-color: rgba( 134, 134, 134, .045 );
}

.tooltipped {
    position: relative;
}

.tooltipped:after {
    background: rgba( 0, 0, 0, 1 );
    border: 1px solid rgba( 119, 119, 119, 1 );
    border-radius: 3px;
    color: rgba( 255, 255, 255, 1 );
    content: attr(aria-label);
    display: none;
    font-family: "Work Sans", "Helvetica", "Tahoma", "Geneva", "Arial", sans-serif;
    font-size: .6875rem;
    font-weight: normal;
    -webkit-font-smoothing: subpixel-antialiased;
    letter-spacing: normal;
    line-height: 1.5;
    padding: 5px 8px;
    pointer-events: none;
    position: absolute;
    text-align: center;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: pre;
    word-wrap: break-word;
    z-index: 140;
}

.tooltipped:before {
    border: 5px solid transparent;
    color: rgba( 0, 0, 0, 1 );
    content: "";
    display: none;
    height: 0;
    pointer-events: none;
    position: absolute;
    width: 0;
    z-index: 150;
}

.tooltipped:hover:before,
.tooltipped:hover:after,
.tooltipped:active:before,
.tooltipped:active:after,
.tooltipped:focus:before,
.tooltipped:focus:after {
    display: inline-block;
    text-decoration: none;
}

.tooltipped-s:after,
.tooltipped-se:after,
.tooltipped-sw:after {
    margin-top: 5px;
    right: 50%;
    top: 100%;
}

.tooltipped-s:before,
.tooltipped-se:before,
.tooltipped-sw:before {
    border-bottom-color: rgba( 0, 0, 0, .8 );
    bottom: -5px;
    margin-right: -5px;
    right: 50%;
    top: auto;
}

.tooltipped-se:after {
    left: 50%;
    margin-left: -15px;
    right: auto;
}

.tooltipped-sw:after {
    margin-right: -15px;
}

.tooltipped-n:after,
.tooltipped-ne:after,
.tooltipped-nw:after {
    bottom: 100%;
    margin-bottom: 5px;
    right: 50%;
}

.tooltipped-n:before,
.tooltipped-ne:before,
.tooltipped-nw:before {
    border-top-color: rgba( 0, 0, 0, .8 );
    bottom: auto;
    margin-right: -5px;
    right: 50%;
    top: -5px;
}

.tooltipped-ne:after {
    left: 50%;
    margin-left: -15px;
    right: auto;
}

.tooltipped-nw:after {
    margin-right: -15px;
}

.tooltipped-s:after,
.tooltipped-n:after {
    transform: translateX(50%);
}

.tooltipped-w:after {
    bottom: 50%;
    margin-right: 5px;
    right: 100%;
    transform: translateY(50%);
}

.tooltipped-w:before {
    border-left-color: rgba( 0, 0, 0, .8 );
    bottom: 50%;
    left: -5px;
    margin-top: -5px;
    top: 50%;
}

.tooltipped-e:after {
    bottom: 50%;
    left: 100%;
    margin-left: 5px;
    transform: translateY(50%);
}

.tooltipped-e:before {
    border-right-color: rgba( 0, 0, 0, .8 );
    bottom: 50%;
    margin-top: -5px;
    right: -5px;
    top: 50%;
}

#R-topbar {
    min-height: 3rem;
    position: relative;
    z-index: 170;
}

#R-topbar > .topbar-wrapper {
    align-items: center;
    background-color: rgba( 134, 134, 134, .066 );
    display: flex;
    flex-basis: 100%;
    flex-direction: row;
    height: 100%;
}

.topbar-button {
    display: inline-block;
    position: relative;
}
.topbar-button:not([data-origin]) {
    display: none;
}

.topbar-button > .topbar-control {
    display: inline-block;
    padding-left: 1rem;
    padding-right: 1rem;
}
.topbar-wrapper > .topbar-area-start > .topbar-button > .topbar-control {
    border-inline-end: 1px solid rgba( 134, 134, 134, .333 );
}
.topbar-wrapper > .topbar-area-end > .topbar-button > .topbar-control {
    border-inline-start: 1px solid rgba( 134, 134, 134, .333 );
}

.topbar-button > button:disabled i,
.topbar-button > span i {
    color: rgba( 134, 134, 134, .333 );
}
.topbar-button button{
    -webkit-appearance: none;
    appearance: none;
    background-color: transparent;
}

.topbar-sidebar-divider {
    border-inline-start-style: solid;
    border-inline-start-width: 1px;
    margin-inline-end: -1px;
    width: 1px;
}
.topbar-sidebar-divider::after {
    content: "\00a0";
}

.topbar-wrapper > .topbar-area-start {
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
}
.topbar-wrapper > .topbar-area-end {
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
}
.topbar-wrapper > .topbar-hidden {
    display: none;
}

html[dir="rtl"] .topbar-button-prev i,
html[dir="rtl"] .topbar-button-next i {
    transform: scaleX(-1);
}

.topbar-content {
    top: .75rem;
}
.topbar-wrapper > .topbar-area-start .topbar-content {
    inset-inline-start: 1.5rem;
}
.topbar-wrapper > .topbar-area-end .topbar-content {
    inset-inline-end: 1.5rem;
}
.topbar-content .topbar-content{
    /* we don't allow flyouts in flyouts; come on, don't get funny... */
    display: none;
}

.topbar-breadcrumbs {
    flex-grow: 1;
    margin: 0;
    padding: 0 1rem;
}
@media screen and (max-width: 47.999rem) {
    .topbar-breadcrumbs {
        /* we just hide the breadcrumbs instead of display: none;
        this makes sure that the breadcrumbs are still usable for
        accessability */
        visibility: hidden;
    }
}

.breadcrumbs {
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    white-space: nowrap;
}

.breadcrumbs meta {
    display: none;
}

.breadcrumbs li {
    display: inline-block;
}

#R-body a[aria-disabled="true"] {
    pointer-events: none;
    text-decoration: none;
}

@media screen and (max-width: 59.999rem) {
    #R-sidebar {
        min-width: var(--INTERNAL-MENU-WIDTH-M);
        max-width: var(--INTERNAL-MENU-WIDTH-M);
        width: var(--INTERNAL-MENU-WIDTH-M);
    }
    #R-body {
        margin-inline-start: var(--INTERNAL-MENU-WIDTH-M);
        min-width: calc( 100% - var(--INTERNAL-MENU-WIDTH-M) );
        max-width: calc( 100% - var(--INTERNAL-MENU-WIDTH-M) );
        width: calc( 100% - var(--INTERNAL-MENU-WIDTH-M) );
    }
}
@media screen and (max-width: 47.999rem) {
    /* we don't support sidebar flyout in mobile */
    .mobile-support #R-sidebar {
        inset-inline-start: calc( -1 * var(--INTERNAL-MENU-WIDTH-S) );
        min-width: var(--INTERNAL-MENU-WIDTH-S);
        max-width: var(--INTERNAL-MENU-WIDTH-S);
        width: var(--INTERNAL-MENU-WIDTH-S);
    }
    .mobile-support #navshow{
        display: inline;
    }
    .mobile-support #R-body {
        min-width: 100%;
        max-width: 100%;
        width: 100%;
    }
    .mobile-support #R-body {
        margin-inline-start: 0;
    }
    .mobile-support.sidebar-flyout {
        overflow: hidden;
    }
    .mobile-support.sidebar-flyout #R-sidebar {
        inset-inline-start: 0;
        z-index: 90;
    }
    .mobile-support.sidebar-flyout #R-body {
        margin-inline-start: var(--INTERNAL-MENU-WIDTH-S);
        overflow: hidden;
    }
    .mobile-support.sidebar-flyout #R-body-overlay{
        background-color: rgba( 134, 134, 134, .5 );
        bottom: 0;
        cursor: pointer;
        height: 100vh;
        left: 0;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 190;
    }
}

.copy-to-clipboard-button {
    border-start-start-radius: 0;
    border-start-end-radius: 2px;
    border-end-end-radius: 2px;
    border-end-start-radius: 0;
    border-style: solid;
    border-width: 1px;
    cursor: pointer;
    font-size: .934375rem;
    line-height: 1.15;
}

span > .copy-to-clipboard-button {
    border-start-start-radius: 0;
    border-start-end-radius: 2px;
    border-end-end-radius: 2px;
    border-end-start-radius: 0;
}

.copy-to-clipboard-button > i {
    font-size: .859625rem;
}

/* only show copy to clipboard on hover for code blocks if configured */
div.highlight .copy-to-clipboard-button {
    display: none;
}
@media (any-hover: none) {
    /* if there is at least one input device that does not support hover, we want to force the copy button */
    div.highlight .copy-to-clipboard-button {
        display: block;
    }
}
div.highlight:hover .copy-to-clipboard-button {
    display: block;
}
.disableHoverBlockCopyToClipBoard div.highlight .copy-to-clipboard-button {
    display: block;
}

div.highlight > div table + .copy-to-clipboard-button > i,
div.highlight pre:not(.mermaid) + .copy-to-clipboard-button > i,
.copy-to-clipboard-code + .copy-to-clipboard-button > i {
    padding-left: 5px;
    padding-right: 5px;
}

div.highlight > div table + .copy-to-clipboard-button,
div.highlight pre:not(.mermaid) + .copy-to-clipboard-button,
pre:not(.mermaid) > .copy-to-clipboard-button {
    background-color: rgba( 160, 160, 160, .2 );
    border-radius: 2px;
    border-style: solid;
    border-width: 1px;
    right: 4px;
    padding: 5px 3px;
    position: absolute;
    top: 4px;
}

.disableInlineCopyToClipboard span > code.copy-to-clipboard-code + span.copy-to-clipboard-button {
    display: none;
}

.disableInlineCopyToClipboard span > code.copy-to-clipboard-code {
    border-start-end-radius: 2px;
    border-end-end-radius: 2px;
    border-inline-end-width: 1px;
}

#R-homelinks {
    padding: 0;
}
#R-homelinks ul {
    margin: .5rem 0;
}
#R-homelinks hr {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    margin: 0 1rem 3px 1rem;
}

option {
    color: initial;
}

.expand {
    margin-bottom: 1rem;
    margin-top: 1rem;
    position: relative;
}

.expand > input {
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

.expand > label {
    cursor: pointer;
    display: inline;
    font-weight: 300;
    inset-inline-start: 0;
    line-height: 1.1;
    margin-top: .2rem;
    position: absolute;
}

.expand > input:active + label,
.expand > input:focus + label,
.expand > label:hover {
    text-decoration: underline;
}

.expand > label > .fas {
    font-size: .8rem;
    width: .6rem;
}

.expand > .expand-content {
    margin-inline-start: 1rem;
    margin-top: .5rem;
}
/* closed expander */
.expand > input + label + div {
    display: none;
}

.expand > input + label > .fa-chevron-down {
    display: none;
}
.expand > input + label > .fa-chevron-right {
    display: inline-block;
}

/* open expander */
.expand > input:checked + label + div {
	display: block;
}

.expand > input:checked + label > .fa-chevron-down {
    display: inline-block;
}
.expand > input:checked + label > .fa-chevron-right {
    display: none;
}

/* adjust expander for RTL reading direction */
html[dir="rtl"] .expand > .expand-label > i.fa-chevron-right {
    transform: scaleX(-1);
}

#R-body footer.footline{
    margin-top: 2rem;
}

.headline i,
.footline i{
    margin-inline-start: .5rem;
}
.headline i:first-child,
.footline i:first-child{
    margin-inline-start: 0;
}

.mermaid-container {
    margin-bottom: 1.7rem;
    margin-top: 1.7rem;
}

.mermaid {
    display: inline-block;
    border: 1px solid transparent;
    padding: .5rem .5rem 0 .5rem;
    position: relative;
    /* don't use display: none, as this will cause no renderinge by Mermaid */
    visibility: hidden;
    width: 100%;
}
.mermaid-container.zoomable > .mermaid:hover {
    border-color: rgba( 134, 134, 134, .333 );
}
.mermaid.mermaid-render {
    visibility: visible;
}

.mermaid > svg {
    /* remove inline height from generated diagram */
    height: initial !important;
}
.mermaid-container.zoomable > .mermaid > svg {
    cursor: grab;
}

.svg-reset-button {
    background-color: rgba( 160, 160, 160, .2 );
    border-radius: 2px;
    border-style: solid;
    border-width: 1px;
    cursor: pointer;
    display: none;
    font-size: .934375rem;
    line-height: 1.15;
    padding: 5px 3px;
    position: absolute;
    right: 4px;
    top: 4px;
}
.mermaid:hover .svg-reset-button.zoomed {
    display: block;
}
@media (any-hover: some) {
    /* if there is at least one input device that does not support hover, we want to force the reset button if zoomed */
    .svg-reset-button.zoomed {
        display: block;
    }
}

.svg-reset-button > i {
    font-size: .859625rem;
    padding-left: 5px;
    padding-right: 5px;
}

.mermaid-code {
    display: none;
}

.include.hide-first-heading h1:first-of-type,
.include.hide-first-heading h2:first-of-type,
.include.hide-first-heading h3:first-of-type,
.include.hide-first-heading h4:first-of-type,
.include.hide-first-heading h5:first-of-type,
.include.hide-first-heading h6:first-of-type {
    display: none;
}

.include.hide-first-heading h1 + h2:first-of-type,
.include.hide-first-heading h1 + h3:first-of-type,
.include.hide-first-heading h2 + h3:first-of-type,
.include.hide-first-heading h1 + h4:first-of-type,
.include.hide-first-heading h2 + h4:first-of-type,
.include.hide-first-heading h3 + h4:first-of-type,
.include.hide-first-heading h1 + h5:first-of-type,
.include.hide-first-heading h2 + h5:first-of-type,
.include.hide-first-heading h3 + h5:first-of-type,
.include.hide-first-heading h4 + h5:first-of-type,
.include.hide-first-heading h1 + h6:first-of-type,
.include.hide-first-heading h2 + h6:first-of-type,
.include.hide-first-heading h3 + h6:first-of-type,
.include.hide-first-heading h4 + h6:first-of-type,
.include.hide-first-heading h5 + h6:first-of-type {
    display: block;
}

/* Table of contents */

.topbar-flyout #R-main-overlay{
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: absolute;
    right: 0;
    top: 3rem;
    z-index: 160;
}

.topbar-content {
    border: 0 solid rgba( 134, 134, 134, .166 );
    box-shadow: 1px 2px 5px 1px rgba( 134, 134, 134, .2 );
    height: 0;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    visibility: hidden;
    width: 0;
    z-index: 180;
}

.topbar-button.topbar-flyout .topbar-content {
    border-width: 1px;
    height: auto;
    opacity: 1;
    visibility: visible;
    width: auto;
}

.topbar-content .topbar-content-wrapper {
    background-color: rgba( 134, 134, 134, .066 );
}

.topbar-content-wrapper {
    --ps-rail-hover-color: rgba( 176, 176, 176, .25 );
    max-height: 90vh;
    overflow: hidden;
    padding: .5rem 1rem;
    position: relative; /* PS */
}

.topbar-content .topbar-button .topbar-control {
    border-width: 0;
    padding: 0;
}
.topbar-content .topbar-button .topbar-control {
    border-width: 0;
    padding: .5rem 0;
}

#TableOfContents,
.TableOfContents {
    font-size: .8125rem;
}
#TableOfContents ul,
.TableOfContents ul {
    list-style: none;
    margin: 0;
    padding: 0 1rem;
}

#TableOfContents > ul,
.TableOfContents > ul {
    padding: 0;
}

#TableOfContents li,
.TableOfContents li {
    white-space: nowrap;
}

#TableOfContents > ul > li > a,
.TableOfContents > ul > li > a {
    font-weight: 500;
}

.btn {
    border-radius: 4px;
    display: inline-block;
    font-size: .9rem;
    font-weight: 500;
    line-height: 1.1;
    margin-bottom: 0;
    touch-action: manipulation;
    -webkit-user-select: none;
    user-select: none;
}
.btn.interactive {
    cursor: pointer;
}

.btn > span,
.btn > a {
    display: block;
}

.btn > :where(button) {
    -webkit-appearance: none;
    appearance: none;
    border-width: 0;
    margin: 0;
    padding: 0;
}

.btn > * {
    background-color: transparent;
    border-radius: 4px;
    border-style: solid;
    border-width: 1px;
    padding: 6px 12px;
    text-align: center;
    touch-action: manipulation;
    -webkit-user-select: none;
    user-select: none;
    white-space: nowrap;
}

.btn > *:after {
    /* avoid breakage if no content is given */
    content: "\200b"
}

#R-body #R-body-inner .btn > *.highlight:after {
    background-color: transparent;
}

.btn.interactive > .btn-interactive:focus {
    outline: none;
}

.btn.interactive > *:hover,
.btn.interactive > *:active,
.btn.interactive > *:focus {
    text-decoration: none;
}

/* anchors */
.anchor {
    cursor: pointer;
    font-size: .5em;
    margin-inline-start: .66em;
    margin-top: .9em;
    position: absolute;
    visibility: hidden;
}
@media (any-hover: none) {
    /* if there is at least one input device that does not support hover, we want to force the copy button */
    .anchor {
        visibility: visible;
    }
}

h2:hover .anchor,
h3:hover .anchor,
h4:hover .anchor,
h5:hover .anchor,
h6:hover .anchor {
    visibility: visible;
}

/* Redfines headers style */

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
    font-weight: inherit;
}

#R-body h1 + h2,
#R-body h1 + h3,
#R-body h1 + h4,
#R-body h1 + h5,
#R-body h1 + h6,
#R-body h2 + h3,
#R-body h2 + h4,
#R-body h2 + h5,
#R-body h2 + h6,
#R-body h3 + h4,
#R-body h3 + h5,
#R-body h3 + h6,
#R-body h4 + h5,
#R-body h4 + h6,
#R-body h5 + h6 {
    margin-top: 1rem;
}

.menu-control .control-style {
    cursor: pointer;
    height: 1.574em;
    overflow: hidden;
}

.menu-control i {
    padding-top: .25em;
}

.menu-control i,
.menu-control span {
    cursor: pointer;
    display: block;
    float: left;
}
html[dir="rtl"] .menu-control i,
html[dir="rtl"] .menu-control span {
    float: right;
}

.menu-control :hover,
.menu-control i:hover,
.menu-control span:hover {
    cursor: pointer;
}

.menu-control select,
.menu-control button {
    -webkit-appearance: none;
    appearance: none;
    outline: none;
    width: 100%;
}
.menu-control button:active,
.menu-control button:focus,
.menu-control select:active,
.menu-control select:focus{
    outline-style: auto;
}

.menu-control select {
    background-color: transparent;
    background-image: none;
    border: none;
    box-shadow: none;
    padding-left: 0;
    padding-right: 0;
}

.menu-control option {
    color: rgba( 0, 0, 0, 1 );
    padding: 0;
    margin: 0;
}

.menu-control button {
    background-color: transparent;
	cursor: pointer;
    display: block;
    text-align: start;
}

.clear {
    clear: both;
}

.footerLangSwitch,
.footerVariantSwitch,
.footerVisitedLinks,
.footerFooter {
    display: none;
}

.showLangSwitch,
.showVariantSwitch,
.showVisitedLinks,
.showFooter {
    display: block;
}

/* clears the 'X' from Chrome's search input */
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration { display: none; }

span.math:has(> mjx-container[display]) {
    display: block;
}

@supports selector(.math:has(> mjx-container)){
    .math{
        visibility: hidden;
    }
    .math:has(> mjx-container){
        visibility: visible;
    }
}
.math.align-left > mjx-container{
    text-align: left !important;
}

.math.align-center > mjx-container{
    text-align: center !important;
}

.math.align-right > mjx-container{
    text-align: right !important;
}

.scrollbar-measure {
    /* https://davidwalsh.name/detect-scrollbar-width */
	height: 100px;
	overflow: scroll;
	position: absolute;
	width: 100px;
	top: -9999px;
}

.a11y-only {
    /* idea taken from https://www.filamentgroup.com/lab/a11y-form-labels.html */
    clip-path: polygon(0 0, 1px 0, 1px 1px, 0 1px);
    overflow: hidden;
    position: absolute;
    height: 1px;
    transform: translateY(-100%);
    transition: transform .5s cubic-bezier(.18,.89,.32,1.28);
    white-space: nowrap;
    width: 1px;
}

/* filament style for making action visible on focus - not adapted yet
.a11y-only:focus {
    position: fixed;
    height: auto;
    overflow: visible;
    clip: auto;
    white-space: normal;
    margin: 0 0 0 -100px;
    top: -.3em;
    left: 50%;
    text-align: center;
    width: 200px;
    background: rgba( 255, 255, 255, 1 );
    color: rgba( 54, 133, 18, 1 );
    padding: .8em 0 .7em;
    font-size: 16px;
    z-index: 5000;
    text-decoration: none;
    border-bottom-right-radius: 8px;
    border-bottom-left-radius: 8px;
    outline: 0;
    transform: translateY(0%);
}
*/

.mermaid-container.align-right  {
    text-align: right;
}

.mermaid-container.align-center  {
    text-align: center;
}

.mermaid-container.align-left {
    text-align: left;
}

.searchform {
    display: flex;
}

.searchform input {
    flex: 1 0 60%;
    border-radius: 4px;
    border: 2px solid rgba( 134, 134, 134, .125 );
    background: rgba( 134, 134, 134, .125 );
    display: block;
    margin: 0;
    margin-inline-end: .5rem;
}

.searchform input::-webkit-input-placeholder,
.searchform input::placeholder {
    color: rgba( 134, 134, 134, 1 );
    opacity: .666;
}

.searchform .btn {
    display: inline-flex;
}

.searchhint {
    margin-top: 1rem;
    height: 1.5rem;
}

#R-searchresults a.autocomplete-suggestion {
    display: block;
    font-size: 1.3rem;
    font-weight: 500;
    line-height: 1.5rem;
    padding: 1rem;
    text-decoration: none;
}

#R-searchresults a.autocomplete-suggestion:after {
    height: 0;
}

#R-searchresults .autocomplete-suggestion > .breadcrumbs {
    font-size: .9rem;
    font-weight: 400;
    margin-top: .167em;
    padding-left: .2em;
    padding-right: .2em;
}

#R-searchresults .autocomplete-suggestion > .context {
    font-size: 1rem;
    font-weight: 300;
    margin-top: .66em;
    padding-left: .1em;
    padding-right: .1em;
}

.badge {
    border-radius: 3px;
    display: inline-block;
    font-size: .8rem;
    font-weight: 500;
    vertical-align: middle;
}

.badge > * {
    border-radius: 3px;
    border-style: solid;
    border-width: 1px;
    display: inline-block;
    padding: 0 .25rem
}

.badge > .badge-title {
    background-color: rgba( 16, 16, 16, 1 );
    border-inline-end: 0;
    border-start-end-radius: 0;
    border-end-end-radius: 0;
    color: rgba( 240, 240, 240, 1 );
    filter: contrast(2);
    opacity: .75;
}

.badge.badge-with-title > .badge-content {
    border-start-start-radius: 0;
    border-end-start-radius: 0;
}

.badge-content:after {
    /* avoid breakage if no content is given */
    content: "\200b";
}

/* task list and its checkboxes */
article ul > li:has(> input[type="checkbox"]) {
    list-style: none;
    margin-inline-start: -1rem;
}

article ul > li:has(> input[type="checkbox"])::before {
    content: "\200B"; /* accessibilty for Safari https://developer.mozilla.org/en-US/docs/Web/CSS/list-style */
}

/* https://moderncss.dev/pure-css-custom-checkbox-style/ */
article ul > li > input[type="checkbox"] {
    -webkit-appearance: none;
    appearance: none;
    /* For iOS < 15 */
    border: 0.15em solid currentColor;
    border-radius: 0.15em;
    display: inline-grid;
    font: inherit;
    height: 1.15em;
    margin: 0;
    place-content: center;
    transform: translateY(-0.075em);
    width: 1.15em;
}

article ul > li > input[type="checkbox"]::before {
    box-shadow: inset 1em 1em var(--INTERNAL-PRIMARY-color);
    clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
    content: "";
    height: 0.65em;
    transform: scale(0);
    transform-origin: bottom left;
    transition: 120ms transform ease-in-out;
    width: 0.65em;
    /* Windows High Contrast Mode fallback must be last */
    background-color: CanvasText;
}

article ul > li > input[type="checkbox"]:checked::before {
    transform: scale(1);
}

/* CSS Lightbox https://codepen.io/gschier/pen/kyRXVx */
.lightbox-back {
    align-items: center;
    background: rgba( 0, 0, 0, .8 );
    bottom: 0;
    display: none;
    justify-content: center;
    left: 0;
    position: fixed;
    right: 0;
    text-align: center;
    top: 0;
    white-space: nowrap;
    z-index: 1999;
}

.lightbox-back:target {
    display: flex;
}

.lightbox-back img {
    max-height: 95%;
    max-width: 95%;
    overflow: auto;
    padding: min(2vh, 2vw);
}

/* basic menu list styles (non-collapsible) */

#R-sidebar ul > li > :is( a, span ) {
    display: block;
    position: relative;
}

#R-sidebar ul.space > li > * {
    padding-bottom: .125rem;
    padding-top: .125rem;
}
#R-sidebar ul.space > li > ul {
    padding-bottom: 0;
    padding-top: 0;
}

#R-sidebar ul.morespace > li > * {
    padding-bottom: .25rem;
    padding-top: .25rem;
}
#R-sidebar ul.morespace > li > ul {
    padding-bottom: 0;
    padding-top: 0;
}

#R-sidebar ul.enlarge > li > :is( a, span ) {
    font-size: 1.1rem;
    line-height: 2rem;
}
#R-sidebar ul.enlarge > li > a > .read-icon {
    margin-top: .5rem;
}
#R-sidebar ul.enlarge > li > ul > li:last-child {
    padding-bottom: 1rem;
}

#R-sidebar ul ul {
    padding-inline-start: 1rem;
}

/* collapsible menu style overrides */

#R-sidebar ul.collapsible-menu > li {
    position: relative;
}

#R-sidebar ul.collapsible-menu > li > input {
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
    display: inline-block;
    margin-left: 0;
    margin-right: 0;
    margin-top: .65rem;
    position: absolute;
    width: 1rem;
    z-index: 1;
}
#R-sidebar ul.collapsible-menu.enlarge > li > input {
    margin-top: .9rem;
}

#R-sidebar ul.collapsible-menu > li > label {
    cursor: pointer;
    display: inline-block;
    inset-inline-start: 0;
    margin-bottom: 0; /* nucleus */
    padding-inline-start: .125rem;
    position: absolute;
    width: 1rem;
    z-index: 2;
}
#R-sidebar ul.collapsible-menu.enlarge > li > label {
    font-size: 1.1rem;
    line-height: 2rem;
}

#R-sidebar ul.collapsible-menu > li > label:after {
    content: "";
    display: block;
    height: 1px;
    transition: width 0.5s ease;
    width: 0%;
}

#R-sidebar ul.collapsible-menu > li > label:hover:after {
    width: 100%;
}

#R-sidebar ul.collapsible-menu > li > label > .fas {
    font-size: .8rem;
    width: .6rem;
}

#R-sidebar ul.collapsible-menu > li > :is( a, span ) {
    display: inline-block;
    width: 100%;
}

/* menu states for not(.collapsible-menu) */

#R-sidebar ul ul {
    display: none;
}

#R-sidebar ul > li.parent > ul,
#R-sidebar ul > li.active > ul,
#R-sidebar ul > li.alwaysopen > ul {
    display: block;
}

/* closed menu */

#R-sidebar ul.collapsible-menu > li > input + label ~ ul {
    display: none;
}

#R-sidebar ul.collapsible-menu > li > input + label > .fa-chevron-down {
    display: none;
}
#R-sidebar ul.collapsible-menu > li > input + label > .fa-chevron-right {
    display: inline-block;
}

/* open menu  */

#R-sidebar ul.collapsible-menu > li > input:checked + label ~ ul {
	display: block;
}

#R-sidebar ul.collapsible-menu > li > input:checked + label > .fa-chevron-down {
    display: inline-block;
}
#R-sidebar ul.collapsible-menu > li > input:checked + label > .fa-chevron-right {
    display: none;
}

/* adjust menu for RTL reading direction */

html[dir="rtl"] #R-sidebar ul.collapsible-menu > li > label > i.fa-chevron-right {
    transform: scaleX(-1);
}

.columnize{
    column-count: 2;
}
@media screen and (min-width: 79.25rem) {
    .columnize{
        column-count: 3;
    }
}

.columnize > *{
    break-inside: avoid-column;
}

.columnize .breadcrumbs{
    font-size: .859625rem;
}

#R-body .tab-panel{
    margin-bottom: 1.5rem;
    margin-top: 1.5rem;
}

#R-body .tab-nav{
    display: flex;
    flex-wrap: wrap;
}

#R-body .tab-nav-title{
    font-size: .9rem;
    font-weight: 400;
    line-height: 1.42857143;
    padding: .2rem 0;
    margin-inline-start: .6rem;
}

#R-body .tab-nav-button{
    -webkit-appearance: none;
    appearance: none;
    background-color: transparent;
    border: 1px solid transparent;
    display: block;
    font-size: .9rem;
    font-weight: 300;
    line-height: 1.42857143;
    margin-inline-start: .6rem;
}

#R-body .tab-nav-button.active{
    border-radius: 2px 2px 0 0;
    cursor: default;
}

#R-body .tab-nav-button > .tab-nav-text{
    border-bottom-style: solid;
    border-bottom-width: .15rem;
    display: block;
    padding: .2rem .6rem 0 .6rem;
}
/* https://stackoverflow.com/a/46452396 */
#R-body .tab-nav-button.active > .tab-nav-text{
    border-bottom-color: transparent;
    border-radius: 1px 1px 0 0;
    text-shadow: -0.06ex 0 0 currentColor, 0.06ex 0 0 currentColor;
}
@supports (-webkit-text-stroke-width: 0.04ex){
    #R-body .tab-nav-button.active > .tab-nav-text{
        text-shadow: -0.03ex 0 0 currentColor, 0.03ex 0 0 currentColor;
        -webkit-text-stroke-width: 0.04ex;
    }
}

#R-body .tab-content{
    border-style: solid;
    border-width: 1px;
    display: none;
    /* if setting a border to 1px, a browser instead sets it to 1dppx which is not
    usable as a unit yet, so we have to calculate it ourself */
    margin-top: calc( -1px / var(--bpx) );
    z-index: 10;
}

#R-body .tab-content.active{
    display: block;
}

#R-body .tab-content-text{
    padding: 1rem;
}

/* remove margin if only a single code block is contained in the tab (FF without :has using .codify style) */
#R-body .tab-content.codify > .tab-content-text{
    padding: 0;
}
#R-body .tab-content-text:has(> div.highlight:only-child){
    padding: 0;
}

/* remove border from code block if single in tab */
#R-body .tab-content-text > div.highlight:only-child > div,
#R-body .tab-content-text > div.highlight:only-child pre:not(.mermaid),
#R-body .tab-content-text > pre:not(.mermaid).pre-code:only-child{
    border-width: 0;
}

/* bordering the menu and topbar */

#R-topbar {
    border-bottom-style: solid;
    border-bottom-width: 1px;
}

#R-header-topbar {
    border-bottom-color: transparent;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-inline-end-style: solid;
    border-inline-end-width: 1px;
    height: 3rem;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1;
}

#R-header-wrapper,
#R-homelinks,
#R-content-wrapper > * {
    border-inline-end-style: solid;
    border-inline-end-width: 1px;
}

#topics > ul {
    margin-top: 1rem;
}

#R-sidebar ul.collapsible-menu li.active > a{
    border-style: solid;
    border-width: 1px;
    padding-bottom: calc( .25rem - var(--bpx1)*1px);
    padding-left: calc( 1rem - var(--bpx1)*1px);
    padding-right: calc( 1rem - var(--bpx1)*1px);
    padding-top: calc( .25rem - var(--bpx1)*1px);
    width: calc(100% + var(--bpx1)*1px);
}

#R-menu-footer {
    padding-bottom: 1rem;
}

#R-topics {
    padding-top: 1rem;
}

.term-list ul,
.term-list li {
    list-style: none;
    display: inline;
    padding: 0;
}
.term-list i ~ ul > li:before{
    content: " "
}
.term-list ul > li ~ li:before {
    content: " | "
}
