.autocomplete-suggestions {
    text-align: start;
    color-scheme: only light; /* set browser scrollbar color */
    cursor: default;
    border: 1px solid rgba( 204, 204, 204, 1 );
    border-top: 0;
    background: rgba( 255, 255, 255, 1 );
    box-shadow: -1px 1px 3px rgba( 0, 0, 0, .1 );
    width: calc( 100% - 2rem );

    /* core styles should not be changed */
    position: absolute;
    display: none;
    z-index: 9999;
    max-height: 10em;
    max-height: calc( 100vh - 10em );
    overflow: hidden;
    overflow-y: auto;
    box-sizing: border-box;
}
.autocomplete-suggestion {
    position: relative;
    cursor: pointer;
    padding: .46em;
    line-height: 1.5em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: rgba( 40, 40, 40, 1 );
}

.autocomplete-suggestion b {
    font-weight: normal;
    color: rgba( 31, 141, 214, 1 );
}

.autocomplete-suggestion.selected {
    background: rgba( 40, 40, 40, 1 );
    color: rgba( 255, 255, 255, 1 );
}

.autocomplete-suggestion:hover,
.autocomplete-suggestion:focus,
.autocomplete-suggestion:active,
.autocomplete-suggestion:hover > .context,
.autocomplete-suggestion:focus > .context,
.autocomplete-suggestion:active > .context,
#R-searchresults .autocomplete-suggestion:hover > .context,
#R-searchresults .autocomplete-suggestion:focus > .context,
#R-searchresults .autocomplete-suggestion:active > .context {
    background: rgba( 56, 56, 56, 1 );
    color: rgba( 255, 255, 255, 1 );
}

.autocomplete-suggestion > .breadcrumbs {
    font-size: .7869em;
    margin-inline-start: 1em;
    overflow: hidden;
    text-overflow: ellipsis;
}

.autocomplete-suggestion > .context {
    font-size: .7869em;
    margin-inline-start: 1em;
    overflow: hidden;
    text-overflow: ellipsis;
}
