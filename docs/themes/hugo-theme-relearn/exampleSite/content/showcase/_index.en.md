+++
title = "Showcase"
[_build]
  render = "always"
  list = "never"
  publishResources = true
+++

## Poésieland by <PERSON>

A collection of poems with requirements for improved taxonomy support, adjusted menu width and additional JavaScript dependencies.

[![Poésieland image](poesieland.png?width=60pc&lightbox=false)](http://poesieland.free.fr/)

## GoboLinux Wiki by NEONsys.org

A Linux distribution wiki with customized CSS and the inspiration for the theme's Neon variant.

[![GoboLinux image](gobolinux.png?width=60pc&lightbox=false)](https://wiki.gobolinux.org/)

## BITS by <PERSON><PERSON><PERSON>llan

Governmental IT security training with additional requirements for accessibility and title customization.

[![BITS image](bits-train.png?width=60pc&lightbox=false)](https://bits-training.de/training/)

## Pamasol Electrics by Pamasol

A classical documentation site in the domain of automation technology with a custom branding variant.

[![Pamasol Electrics](pamasol-electrics-portal.png?width=60pc&lightbox=false)](https://pamasol.github.io/de/)
