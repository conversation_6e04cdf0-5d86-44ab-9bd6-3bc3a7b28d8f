+++
archetype = "home"
title = "Hugo Relearn Theme"
+++

A theme for [<PERSON>](https://gohugo.io/) designed for documentation.

[★ What's new in the latest release ★](basics/migration)

![Image of the Relearn theme in light and dark mode on phone, tablet and desktop](/images/hero.png)

## Motivation

The theme is a fork of the great [Learn theme](https://github.com/matcornic/hugo-theme-learn) with the aim of fixing long outstanding bugs and adapting to latest Hugo features. As far as possible this theme tries to be a drop-in replacement for the Learn theme.

## Features

- **Wide set of usage scenarios**
  - Responsive design for mobile usage
  - Looks nice on paper (if it has to)
  - Usable offline, no external dependencies
  - [Usable from your local file system via `file://` protocol](basics/customization#file-system)
  - Support for the [VSCode Front Matter extension](https://github.com/estruyf/vscode-front-matter) for on-premise CMS capabilities
  - [Support for Open Graph and Twitter Cards](basics/customization#social-media-meta-tags)
- **Configurable theming and visuals**
  - [Configurable brand images](basics/branding#change-the-logo)
  - [Automatic switch for light/dark variant depending on your OS settings](basics/branding#adjust-to-os-settings)
  - Predefined light, dark and color variants
  - [User selectable variants](basics/branding#multiple-variants)
  - [Stylesheet generator](basics/generator/)
  - [Configurable syntax highlighting](shortcodes/highlight)
- **Unique theme features**
  - [Print whole chapters or even the complete site](basics/customization#activate-print-support)
  - In page search
  - [Site search](basics/customization#activate-search)
  - [Dedicated search page](basics/customization#activate-dedicated-search-page)
  - [Taxonomy support](cont/taxonomy)
  - [Configurable topbar buttons](basics/topbar)
  - [Unlimited nested menu items](cont/pages)
  - [Configurable shortcut links](cont/menushortcuts)
  - Hidden pages
- **Multi language support**
  - [Full support for languages written right to left](cont/i18n)
  - [Available languages](cont/i18n#basic-configuration): Arabic, Simplified Chinese, Traditional Chinese, Czech, Dutch, English, Finnish, French, German, Hindi, Hungarian, Indonesian, Italian, Japanese, Korean, Polish, Portuguese, Romanian, Russian, Spanish, Swahili, Turkish, Vietnamese
  - [Search support for mixed language content](cont/i18n#search)
- **Additional Markdown features**
  - [Support for GFM (GitHub Flavored Markdown)](cont/markdown)
  - [Image effects like sizing, shadow, border and alignment](cont/markdown#image-effects)
  - [Image lightbox](cont/markdown#lightbox)
- **Shortcodes galore**
  - [Display resources contained in a page bundle](shortcodes/resources)
  - [Marker badges](shortcodes/badge)
  - [Configurable buttons](shortcodes/button)
  - [List child pages](shortcodes/children)
  - [Expand areas to reveal content](shortcodes/expand)
  - [Font Awesome icons](shortcodes/icon)
  - [Inclusion of other files](shortcodes/include)
  - [Math and chemical formulae using MathJax](shortcodes/math)
  - [Mermaid diagrams for flowcharts, sequences, gantts, pie, etc.](shortcodes/mermaid)
  - [Colorful boxes](shortcodes/notice)
  - [OpenAPI specifications using Swagger UI](shortcodes/openapi)
  - [Reveal you site's configuration parameter](shortcodes/siteparam)
  - [Single tabbed panels](shortcodes/tab) and [multiple tabbed panels](shortcodes/tabs)

## Support

To get support, feel free to open a new [discussion topic](https://github.com/McShelby/hugo-theme-relearn/discussions) or [issue report](https://github.com/McShelby/hugo-theme-relearn/issues) in the official repository on GitHub.

## Contributions

Feel free to contribute to this documentation by just clicking the {{% button style="transparent" icon="pen" %}}{{% /button %}} _edit_ button displayed on top right of each page.

You are most welcome to contribute bugfixes or new features by making pull requests to the [official repository](https://github.com/McShelby/hugo-theme-relearn). Check the [contribution guidelines](dev/contributing) first before starting.

## License

The Relearn theme is licensed under the [MIT License](https://github.com/McShelby/hugo-theme-relearn/blob/main/LICENSE).

## Credits

This theme would not be possible without the work of [many others](more/credits).
