+++
title = "Credits"
+++

## Contributors

Special thanks to [everyone who has contributed](https://github.com/McShelby/hugo-theme-relearn/graphs/contributors) to this project.

Many thanks to [<PERSON><PERSON>](https://github.com/mat<PERSON><PERSON>) for his work on porting the [Learn theme](https://github.com/matcornic/hugo-theme-learn) to <PERSON>.

Many thanks to [<PERSON>](https://github.com/rhukster) for initially creating the [Learn theme](https://github.com/getgrav/grav-theme-learn2) for Grav.

## Theme Dependencies

- [autoComplete](https://github.com/Pixabay/JavaScript-autoComplete) - A lightweight and powerful vanilla JavaScript completion suggester
- [clipboard.js](https://clipboardjs.com) - A modern approach to copy text to clipboard
- [d3-zoom](https://github.com/d3/d3-zoom) - Pan and zoom SVG, HTML or Canvas using mouse or touch input - plus dependencies
  - [d3-color](https://github.com/d3/d3-color) - Color spaces! RGB, HSL, Cubehelix, CIELAB, and more
  - [d3-dispatch](https://github.com/d3/d3-dispatch) - Register named callbacks and call them with arguments
  - [d3-ease](https://github.com/d3/d3-ease) - Easing functions for smooth animation
  - [d3-interpolate](https://github.com/d3/d3-interpolate) - Interpolate numbers, colors, strings, arrays, objects, whatever
  - [d3-selection](https://github.com/d3/d3-selection) - Transform the DOM by selecting elements and joining to data
  - [d3-timer](https://github.com/d3/d3-timer) - An efficient queue for managing thousands of concurrent animations
  - [d3-transition](https://github.com/d3/d3-transition) - Animated transitions for D3 selections
  - [d3-drag](https://github.com/d3/d3-drag) - Drag and drop SVG, HTML or Canvas using mouse or touch input
- [Font Awesome](https://fontawesome.com) - The internet's icon library and toolkit
- [js-yaml](https://github.com/nodeca/js-yaml) - JavaScript YAML parser and dumper
- [Lunr](https://lunrjs.com) - Enables a great search experience without the need for external, server-side, search services
- [Lunr Languages](https://github.com/MihaiValentin/lunr-languages) - A collection of languages stemmers and stopwords for Lunr Javascript library
- [MathJax](https://mathjax.org/) - Beautiful math and chemical formulae in all browsers
- [Mermaid](https://mermaid-js.github.io/mermaid) - Generation of diagram and flowchart from text in a similar manner as markdown
- [Perfect Scrollbar](https://perfectscrollbar.com) - A minimalistic but perfect custom scrollbar plugin
- [SwaggerUI](https://github.com/swagger-api/swagger-ui) - Generate beautiful documentation from a Swagger-compliant API
- [WorkSans](https://weiweihuanghuang.github.io/Work-Sans/) - Work Sans is a 9 weight typeface family based loosely on early Grotesques

## Docs Dependencies

- [github-buttons](https://github.com/buttons/github-buttons) - Unofficial github:buttons

## Tooling Dependencies

- [GitHub](https://github.com) - Continuous deployment, testing and hosting of this project's sources and its documentation
- Various GitHub Actions
  - https://github.com/actions/checkout
  - https://github.com/actions/setup-node
  - https://github.com/Akkjon/close-milestone
  - https://github.com/andstor/file-reader-action
  - https://github.com/ashley-taylor/regex-property-action
  - https://github.com/Kaven-Universe/github-action-current-date-time
  - https://github.com/mingjun97/file-regex-replace
  - https://github.com/octokit/graphql-action
  - https://github.com/peaceiris/actions-gh-pages
  - https://github.com/peaceiris/actions-hugo
  - https://github.com/WyriHaximus/github-action-create-milestone
  - https://github.com/WyriHaximus/github-action-next-semvers
- [gren](https://github.com/github-tools/github-release-notes) - A releasenotes generator for GitHub
- [Hugo](https://gohugo.io/) - The static site generator of your choice
