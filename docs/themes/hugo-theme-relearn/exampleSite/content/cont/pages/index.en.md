+++
title = "Pages organization"
weight = 1
+++

In **<PERSON>**, pages are the core of your site. Once it is configured, pages are definitely the added value to your documentation site.

## Folders

Organize your site like [any other Hugo project](https://gohugo.io/content/organization/). Typically, you will have a _content_ folder with all your pages.

````plaintext
content
├── level-one
│   ├── level-two
│   │   ├── level-three
│   │   │   ├── level-four
│   │   │   │   ├── _index.md       <-- /level-one/level-two/level-three/level-four
│   │   │   │   ├── page-4-a.md     <-- /level-one/level-two/level-three/level-four/page-4-a
│   │   │   │   ├── page-4-b.md     <-- /level-one/level-two/level-three/level-four/page-4-b
│   │   │   │   └── page-4-c.md     <-- /level-one/level-two/level-three/level-four/page-4-c
│   │   │   ├── _index.md           <-- /level-one/level-two/level-three
│   │   │   ├── page-3-a.md         <-- /level-one/level-two/level-three/page-3-a
│   │   │   ├── page-3-b.md         <-- /level-one/level-two/level-three/page-3-b
│   │   │   └── page-3-c.md         <-- /level-one/level-two/level-three/page-3-c
│   │   ├── _index.md               <-- /level-one/level-two
│   │   ├── page-2-a.md             <-- /level-one/level-two/page-2-a
│   │   ├── page-2-b.md             <-- /level-one/level-two/page-2-b
│   │   └── page-2-c.md             <-- /level-one/level-two/page-2-c
│   ├── _index.md                   <-- /level-one
│   ├── page-1-a.md                 <-- /level-one/page-1-a
│   ├── page-1-b.md                 <-- /level-one/page-1-b
│   └── page-1-c.md                 <-- /level-one/page-1-c
├── _index.md                       <-- /
└── page-top.md                     <-- /page-top
````

{{% notice note %}}
`_index.md` is required in each folder, it’s your “folder home page”
{{% /notice %}}

## Create your project

The following steps are here to help you initialize your new website. If you don't know Hugo at all, we strongly suggest you to train by following [great documentation for beginners](https://gohugo.io/overview/quickstart/).

Hugo provides a `new` command to create a new website.

````shell
hugo new site <new_project>
````

The Relearn theme provides [archetypes](cont/archetypes) to help you create this kind of pages.
