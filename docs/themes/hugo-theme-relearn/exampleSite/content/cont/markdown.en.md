+++
tags = ["Content"]
title = "Markdown Syntax"
weight = 4
+++

Let's face it: Writing content for the web is tiresome. WYSIWYG editors help alleviate this task, but they generally result in horrible code, or worse yet, ugly web pages.

**Markdown** is a better way to write **HTML**, without all the complexities and ugliness that usually accompanies it.

Some of the key benefits are:

1. Markdown is simple to learn, with minimal extra characters so it's also quicker to write content.
2. Less chance of errors when writing in Markdown.
3. Produces valid HTML output.
4. Keeps the content and the visual display separate, so you cannot mess up the look of your site.
5. Write in any text editor or Markdown application you like.
6. Markdown is a joy to use!

<PERSON>, the author of Markdown, puts it like this:

> The overriding design goal for <PERSON><PERSON>’s formatting syntax is to make it as readable as possible. The idea is that a Markdown-formatted document should be publishable as-is, as plain text, without looking like it’s been marked up with tags or formatting instructions. While Markdown’s syntax has been influenced by several existing text-to-HTML filters, the single biggest source of inspiration for <PERSON><PERSON>’s syntax is the format of plain text email.
> <cite><PERSON></cite>

{{% notice tip %}}
{{% icon bookmark %}} Bookmark this page for easy future reference!
{{% /notice %}}

## Standard and Extensions

If not otherwise noted, the showed examples adhere to the [Commonmark](https://commonmark.org/help/) standard. In addition the theme supports the following extensions:

- {{% badge color="darkgray" icon="fa-fw fab fa-github" %}}GFM{{% /badge %}} Extension on top of standard Markdown adhering to [GitHub Flavored Markdown](https://github.github.com/gfm/).

- {{% badge color="#888cc4" icon="fa-fw fab fa-markdown" %}}PHP{{% /badge %}} Extension on top of standard Markdown adhering to [PHP Markdown](https://michelf.ca/projects/php-markdown/extra/).

- {{% badge color="darkorange" icon="lightbulb" %}}Pants{{% /badge %}} Extension by John Gruber adhering to [SmartyPants](https://daringfireball.net/projects/smartypants/).

- {{% badge color="#7dc903" icon="fa-fw fas fa-puzzle-piece" %}}Relearn{{% /badge %}} Extension specific to this theme.

- {{% badge color="orangered" icon="fa-fw fas fa-code" %}}HTML{{% /badge %}} If the [usage of HTML](https://gohugo.io/getting-started/configuration-markup/#rendererunsafe) is allowed in your `hugo.toml` the theme supports styling for further elements not accessible using Markdown alone.

## Paragraphs

In Markdown your content usually spans the whole available document width. This is called a block. Blocks are always separated by whitespace to their adjacent blocks in the resulting document.

Any text not starting with a special sign is written as normal, plain text paragraph block and must be separated to its adjacent blocks by empty lines.

````md
Lorem ipsum dolor sit amet, graecis denique ei vel, at duo primis mandamus.

Et legere ocurreret pri, animal tacimates complectitur ad cum. Cu eum inermis inimicus efficiendi. Labore officiis his ex, soluta officiis concludaturque ei qui, vide sensibus vim ad.
````

{{% notice style="secondary" icon="eye" title="Result" %}}
Lorem ipsum dolor sit amet, graecis denique ei vel, at duo primis mandamus.

Et legere ocurreret pri, animal tacimates complectitur ad cum. Cu eum inermis inimicus efficiendi. Labore officiis his ex, soluta officiis concludaturque ei qui, vide sensibus vim ad.
{{% /notice %}}

## Headings

A good idea is to structure your content using headings and subheadings. HTML-headings from `h1` through `h6` are constructed with a `#` for each level.

In Hugo you usually don't use `h1` as this is generated by your theme and you should only have one such element in a document.

````md
# h1 Heading

## h2 Heading

### h3 Heading

#### h4 Heading

##### h5 Heading

###### h6 Heading
````

{{% notice style="secondary" icon="eye" title="Result" %}}

# h1 Heading

## h2 Heading

### h3 Heading

#### h4 Heading

##### h5 Heading

###### h6 Heading
{{% /notice %}}

## Horizontal Rules

To further structure your content you can add horizontal rules. They create a "thematic break" between paragraph blocks. In Markdown, you can create it with three consecutive dashes `---`.

````md
Lorem ipsum dolor sit amet, graecis denique ei vel, at duo primis mandamus.

---

Et legere ocurreret pri, animal tacimates complectitur ad cum. Cu eum inermis inimicus efficiendi. Labore officiis his ex, soluta officiis concludaturque ei qui, vide sensibus vim ad.
````

{{% notice style="secondary" icon="eye" title="Result" %}}
Lorem ipsum dolor sit amet, graecis denique ei vel, at duo primis mandamus.

---

Et legere ocurreret pri, animal tacimates complectitur ad cum. Cu eum inermis inimicus efficiendi. Labore officiis his ex, soluta officiis concludaturque ei qui, vide sensibus vim ad.
{{% /notice %}}

## Text Markers

### Bold

You can show importance of a snippet of text with a heavier font-weight by enclosing it with two asterisks `**`.

````md
I am rendered with **bold text**
````

{{% notice style="secondary" icon="eye" title="Result" %}}
I am rendered with **bold text**
{{% /notice %}}

### Italics

You can emphasize a snippet of text with italics by enclosing it with underscores `_`.

````md
I am rendered with _italicized text_
````

{{% notice style="secondary" icon="eye" title="Result" %}}
I am rendered with _italicized text_
{{% /notice %}}

### Strikethrough

{{% badge color="darkgray" icon="fa-fw fab fa-github" %}}GFM{{% /badge %}} You can do strikethroughs by enclosing text with two tildes `~~`.

````md
~~Strike through this text~~
````

{{% notice style="secondary" icon="eye" title="Result" %}}
~~Strike through this text~~
{{% /notice %}}

### Marked Text

{{% badge color="orangered" icon="fa-fw fas fa-code" %}}HTML{{% /badge %}} You can mark text in the predefined accent color of your stylesheet.

````html
<mark>Parts</mark> of this text <mark>are marked!</mark>
````

{{% notice style="secondary" icon="eye" title="Result" %}}
<mark>Parts</mark> of this text <mark>are marked!</mark>
{{% /notice %}}


## Special Typesetting

### Text Substitution

{{% badge color="darkorange" icon="lightbulb" %}}Pants{{% /badge %}} You can combine multiple punctuation characters to single typographic entities. This will only be applied to text outside of code blocks or inline code.

````md
Double quotes `"` and single quotes `'` of enclosed text are replaced by **"double curly quotes"** and **'single curly quotes'**.

Double dashes `--` and triple dashes `---` are replaced by en-dash **--** and em-dash **---** entities.

Double arrows pointing left `<<` or right `>>` are replaced by arrow **<<** and **>>** entities.

Three consecutive dots `...` are replaced by an ellipsis **...** entity.
````

{{% notice style="secondary" icon="eye" title="Result" %}}
Double quotes `"` and single quotes `'` of enclosed text are replaced by **"double curly quotes"** and **'single curly quotes'**.

Double dashes `--` and triple dashes `---` are replaced by en-dash **--** and em-dash **---** entities.

Double arrows pointing left `<<` or right `>>` are replaced by arrow **<<** and **>>** entities.

Three consecutive dots `...` are replaced by an ellipsis **...** entity.
{{% /notice %}}

### Keyboard Shortcuts

{{% badge color="orangered" icon="fa-fw fas fa-code" %}}HTML{{% /badge %}} You can use the `<kbd>` element to style keyboard shortcuts.

````html
Press <kbd>STRG</kbd> <kbd>ALT</kbd> <kbd>DEL</kbd> to end your shift early.
````

{{% notice style="secondary" icon="eye" title="Result" %}}
Press <kbd>STRG</kbd> <kbd>ALT</kbd> <kbd>DEL</kbd> to end your shift early.
{{% /notice %}}

### Sub and Super Script

{{% badge color="orangered" icon="fa-fw fas fa-code" %}}HTML{{% /badge %}} You can also use the `<sub>` and `<sup>` elements. For more complex stuff or if your configuration does not allow HTML, you can use the [`math` shortcode](shortcodes/math).

````html
How many liters H<sub>2</sub>O fit into 1dm<sup>3</sup>?
````

{{% notice style="secondary" icon="eye" title="Result" %}}
How many liters H<sub>2</sub>O fit into 1dm<sup>3</sup>?
{{% /notice %}}

## Lists

### Unordered

You can write a list of items in which the order of the items does not explicitly matter.

It is possible to nest lists by indenting an item for the next sublevel.

You may use any of `-`, `*` or `+` to denote bullets for each list item but should not switch between those symbols inside one whole list.

````md
- Lorem ipsum dolor sit amet
- Consectetur adipiscing elit
  - Vestibulum laoreet porttitor sem
  - Ac tristique libero volutpat at
- Nulla volutpat aliquam velit
  - Phasellus iaculis neque
  - Purus sodales ultricies
- Faucibus porta lacus fringilla vel
````

{{% notice style="secondary" icon="eye" title="Result" %}}
- Lorem ipsum dolor sit amet
- Consectetur adipiscing elit
  - Vestibulum laoreet porttitor sem
  - Ac tristique libero volutpat at
- Nulla volutpat aliquam velit
  - Phasellus iaculis neque
  - Purus sodales ultricies
- Faucibus porta lacus fringilla vel
{{% /notice %}}

### Ordered

You can create a list of items in which the order of items does explicitly matter.

It is possible to nest lists by indenting an item for the next sublevel.

Markdown will automatically number each of your items consecutively. This means, the order number you are providing is irrelevant.

````md
1. Lorem ipsum dolor sit amet
3. Consectetur adipiscing elit
    1. Integer molestie lorem at massa
    7. Facilisis in pretium nisl aliquet
99. Nulla volutpat aliquam velit
    1. Faucibus porta lacus fringilla vel
    1. Aenean sit amet erat nunc
17. Eget porttitor lorem
````

{{% notice style="secondary" icon="eye" title="Result" %}}
1. Lorem ipsum dolor sit amet
1. Consectetur adipiscing elit
    1. Integer molestie lorem at massa
    7. Facilisis in pretium nisl aliquet
99. Nulla volutpat aliquam velit
    1. Faucibus porta lacus fringilla vel
    1. Aenean sit amet erat nunc
17. Eget porttitor lorem
{{% /notice %}}

### Tasks

{{% badge color="darkgray" icon="fa-fw fab fa-github" %}}GFM{{% /badge %}} You can add task lists resulting in checked or unchecked non-clickable items

````md
- [x] Basic Test
- [ ] More Tests
  - [x] View
  - [x] Hear
  - [ ] Smell
````

{{% notice style="secondary" icon="eye" title="Result" %}}
- [x] Basic Test
- [ ] More Tests
  - [x] View
  - [x] Hear
  - [ ] Smell
{{% /notice %}}

### Definitions

{{% badge color="#888cc4" icon="fa-fw fab fa-markdown" %}}PHP{{% /badge %}} Definition lists are made of terms and definitions of these terms, much like in a dictionary.

A definition list in Markdown Extra is made of a single-line term followed by a colon and the definition for that term. You can also associate more than one term to a definition.

If you add empty lines around the definition terms, additional vertical space will be generated. Also multiple paragraphs are possible

````md
Apple
: Pomaceous fruit of plants of the genus Malus in the family Rosaceae.
: An American computer company.

Orange
: The fruit of an evergreen tree of the genus Citrus.

  You can make juice out of it.
: A telecommunication company.

  You can't make juice out of it.
````

{{% notice style="secondary" icon="eye" title="Result" %}}
Apple
: Pomaceous fruit of plants of the genus Malus in the family Rosaceae.
: An American computer company.

Orange
: The fruit of an evergreen tree of the genus Citrus.

  You can make juice out of it.
: A telecommunication company.

  You can't make juice out of it.
{{% /notice %}}

## Code

### Inline Code

Inline snippets of code can be wrapped with backticks `` ` ``.

````md
In this example, `<div></div>` is marked as code.
````

{{% notice style="secondary" icon="eye" title="Result" %}}
In this example, `<div></div>` is marked as code.
{{% /notice %}}

### Indented Code Block

A simple code block can be generated by indenting several lines of code by at least two spaces.

````md
Be impressed by my advanced code:

    // Some comments
    line 1 of code
    line 2 of code
    line 3 of code
````

{{% notice style="secondary" icon="eye" title="Result" %}}
Be impressed by my advanced code:

    // Some comments
    line 1 of code
    line 2 of code
    line 3 of code
{{% /notice %}}

### Fenced Code Block

If you want to gain more control of your code block you can enclose your code by at least three backticks ```` ``` ```` a so called fence.

{{% badge color="darkgray" icon="fa-fw fab fa-github" %}}GFM{{% /badge %}} You can also add a language specifier directly after the opening fence, ` ```js `, and syntax highlighting will automatically be applied according to the selected language in the rendered HTML.

See [Code Highlighting](shortcodes/highlight) for additional documentation.

````plaintext
```js
{
    name: "Claus",
    surname: "Santa",
    profession: "courier",
    age: 666,
    address: {
        city: "North Pole",
        postalCode: 1,
        country: "Arctic"
    },
    friends: [ "Dasher", "Dancer", "Prancer", "Vixen", "Comet", "Cupid", "Donder", "Blitzen", "Rudolph" ]
};
```
````

{{% notice style="secondary" icon="eye" title="Result" %}}
```js
{
    name: "Claus",
    surname: "Santa",
    profession: "courier",
    age: 666,
    address: {
        city: "North Pole",
        postalCode: 1,
        country: "Arctic"
    },
    friends: [ "Dasher", "Dancer", "Prancer", "Vixen", "Comet", "Cupid", "Donder", "Blitzen", "Rudolph" ]
};
```
{{% /notice %}}

## Tables

{{% badge color="darkgray" icon="fa-fw fab fa-github" %}}GFM{{% /badge %}} You can create tables by adding pipes as dividers between each cell, and by adding a line of dashes (also separated by bars) beneath the header. Note that the pipes do not need to be vertically aligned.

````md
| Option | Description |
|--------|-------------|
| data   | path to data files to supply the data that will be passed into templates. |
| engine | engine to be used for processing templates. Handlebars is the default. |
| ext    | extension to be used for dest files. |
````

{{% notice style="secondary" icon="eye" title="Result" %}}
| Option | Description |
|--------|-------------|
| data   | path to data files to supply the data that will be passed into templates. |
| engine | engine to be used for processing templates. Handlebars is the default. |
| ext    | extension to be used for dest files. |
{{% /notice %}}

### Aligned Columns

Adding a colon on the left and/or right side of the dashes below any heading will align the text for that column accordingly.

````md
| Option | Number | Description |
|-------:|:------:|:------------|
| data   | 1      | path to data files to supply the data that will be passed into templates. |
| engine | 2      | engine to be used for processing templates. Handlebars is the default. |
| ext    | 3      | extension to be used for dest files. |
````

{{% notice style="secondary" icon="eye" title="Result" %}}
| Option | Number | Description |
|-------:|:------:|:------------|
| data   | 1      | path to data files to supply the data that will be passed into templates. |
| engine | 2      | engine to be used for processing templates. Handlebars is the default. |
| ext    | 3      | extension to be used for dest files. |
{{% /notice %}}

## Blockquotes

For quoting blocks of content from another source within your document add `>` before any text you want to quote.

Blockquotes can also be nested.

````md
> Donec massa lacus, ultricies a ullamcorper in, fermentum sed augue. Nunc augue, aliquam non hendrerit ac, commodo vel nisi.
>
> > Sed adipiscing elit vitae augue consectetur a gravida nunc vehicula. Donec auctor odio non est accumsan facilisis. Aliquam id turpis in dolor tincidunt mollis ac eu diam.
>
> Mauris sit amet ligula egestas, feugiat metus tincidunt, luctus libero. Donec congue finibus tempor. Vestibulum aliquet sollicitudin erat, ut aliquet purus posuere luctus.
````

{{% notice style="secondary" icon="eye" title="Result" %}}
> Donec massa lacus, ultricies a ullamcorper in, fermentum sed augue. Nunc augue, aliquam non hendrerit ac, commodo vel nisi.
>
> > Sed adipiscing elit vitae augue consectetur a gravida nunc vehicula. Donec auctor odio non est accumsan facilisis. Aliquam id turpis in dolor tincidunt mollis ac eu diam.
>
> Mauris sit amet ligula egestas, feugiat metus tincidunt, luctus libero. Donec congue finibus tempor. Vestibulum aliquet sollicitudin erat, ut aliquet purus posuere luctus.
{{% /notice %}}

## Links

### Autolink

{{% badge color="darkgray" icon="fa-fw fab fa-github" %}}GFM{{% /badge %}} Absolute URLs will automatically be converted into a link.

````md
This is a link to https://example.com.
````

{{% notice style="secondary" icon="eye" title="Result" %}}
This is a link to https://example.com.
{{% /notice %}}


### Basic Link

You can explicitly define links in case you want to use non-absolute URLs or want to give different text.

````md
[Assemble](http://assemble.io)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
[Assemble](http://assemble.io)
{{% /notice %}}

### Link with Tooltip

For even further information, you can add an additional text, displayed in a tooltip on hovering over the link.

````md
[Upstage](https://github.com/upstage/ "Visit Upstage!")
````

{{% notice style="secondary" icon="eye" title="Result" %}}
[Upstage](https://github.com/upstage/ "Visit Upstage!")
{{% /notice %}}

### Link References

Links can be simplyfied for recurring reuse by using a reference ID to later define the URL location. This simplyfies writing if you want to use a link more than once in a document.

````md
[Example][somelinkID]

[somelinkID]: https://example.com "Go to example domain"
````

{{% notice style="secondary" icon="eye" title="Result" %}}
[Example][somelinkID]

[somelinkID]: https://example.com "Go to example domain"
{{% /notice %}}

### Footnotes

{{% badge color="#888cc4" icon="fa-fw fab fa-markdown" %}}PHP{{% /badge %}} Footnotes work mostly like reference-style links. A footnote is made of two things, a marker in the text that will become a superscript number and a footnote definition that will be placed in a list of footnotes.

Usually the list of footnotes will be shown at the end of your document. If we use a footnote in a notice box it will instead be listed at the end of its box.

Footnotes can contain block elements, which means that you can put multiple paragraphs, lists, blockquotes and so on in a footnote. It works the same as for list items, just indent the following paragraphs by four spaces in the footnote definition.

````md
That's some text with a footnote[^1]

[^1]: And that's the footnote.

That's some more text with a footnote.[^someid]

[^someid]:
    Anything of interest goes here.

    Blue light glows blue.
````

{{% notice style="secondary" icon="eye" title="Result" %}}
That's some text with a footnote[^1]

[^1]: And that's the footnote.

That's some more text with a footnote.[^someid]

[^someid]:
    Anything of interest goes here.

    Blue light glows blue.
{{% /notice %}}

## Images

### Basic Images

Images have a similar syntax to links but include a preceding exclamation mark.

````md
![Spock](https://octodex.github.com/images/spocktocat.png)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![Spock](https://octodex.github.com/images/spocktocat.png?width=20vw)
{{% /notice %}}

### Image with Tooltip

Like links, images can also be given a tooltip.

````md
![Picard](https://octodex.github.com/images/jean-luc-picat.jpg "Jean Luc Picard")
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![Picard](https://octodex.github.com/images/jean-luc-picat.jpg?width=20vw "Jean Luc Picard")
{{% /notice %}}

### Image References

Images can also be linked by reference ID to later define the URL location. This simplyfies writing if you want to use an image more than once in a document.

````md
![La Forge][laforge]

[laforge]: https://octodex.github.com/images/trekkie.jpg "Geordi La Forge"
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![La Forge][laforge]

[laforge]: https://octodex.github.com/images/trekkie.jpg?width=20vw "Geordi La Forge"
{{% /notice %}}

### Image Effects

{{% badge color="#7dc903" icon="fa-fw fas fa-puzzle-piece" %}}Relearn{{% /badge %}} This theme allows additional non-standard formatting by setting query parameter at the end of the image URL. The default [behavior is configurable](cont/imageeffects) through your `hugo.toml` or frontmatter parameter.

#### Resizing

Add query parameter `width` and/or `height` to the link image to resize the image. Values are CSS values (default is `auto`).

````md
![Minion](https://octodex.github.com/images/minion.png?width=20vw)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![Minion](https://octodex.github.com/images/minion.png?width=20vw)
{{% /notice %}}

````md
![Minion](https://octodex.github.com/images/minion.png?height=50px)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![Minion](https://octodex.github.com/images/minion.png?height=50px)
{{% /notice %}}

````md
![Minion](https://octodex.github.com/images/minion.png?height=50px&width=40vw)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![Minion](https://octodex.github.com/images/minion.png?height=50px&width=40vw)
{{% /notice %}}

#### CSS Classes

Add a query parameter `classes` to the link image to add CSS classes. Add some of the predefined values or even define your own in your CSS.

##### Shadow

````md
![Spidertocat](https://octodex.github.com/images/spidertocat.png?classes=shadow)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![Spidertocat](https://octodex.github.com/images/spidertocat.png?width=20vw&classes=shadow,noborder)
{{% /notice %}}

##### Border

````md
![DrOctocat](https://octodex.github.com/images/droctocat.png?classes=border)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![DrOctocat](https://octodex.github.com/images/droctocat.png?width=20vw&classes=border,noshadow)
{{% /notice %}}

##### Left

````md
![Supertocat](https://octodex.github.com/images/okal-eltocat.jpg?classes=left)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![Supertocat](https://octodex.github.com/images/okal-eltocat.jpg?width=20vw&classes=left)
{{% /notice %}}

##### Right

````md
![Riddlocat](https://octodex.github.com/images/riddlocat.jpg?classes=right)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![Riddlocat](https://octodex.github.com/images/riddlocat.jpg?width=20vw&classes=right)
{{% /notice %}}

##### Inline

````md
![Spidertocat](https://octodex.github.com/images/spidertocat.png?classes=inline)
![DrOctocat](https://octodex.github.com/images/droctocat.png?classes=inline)
![Supertocat](https://octodex.github.com/images/okal-eltocat.jpg?classes=inline)
![Riddlocat](https://octodex.github.com/images/riddlocat.jpg?classes=inline)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![Spidertocat](https://octodex.github.com/images/spidertocat.png?width=10vw&classes=inline)
![DrOctocat](https://octodex.github.com/images/droctocat.png?width=10vw&classes=inline)
![Supertocat](https://octodex.github.com/images/okal-eltocat.jpg?width=10vw&classes=inline)
![Riddlocat](https://octodex.github.com/images/riddlocat.jpg?width=10vw&classes=inline)
{{% /notice %}}

##### Combination

````md
![X-tocat](https://octodex.github.com/images/xtocat.jpg?classes=shadow,border,left)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![X-tocat](https://octodex.github.com/images/xtocat.jpg?width=20vw&classes=shadow,border,left)
{{% /notice %}}

#### Lightbox

Add the query parameter `lightbox=false` to the image link to disable the lightbox.

````md
![Homercat](https://octodex.github.com/images/homercat.png?lightbox=false)
````

{{% notice style="secondary" icon="eye" title="Result" %}}
![Homercat](https://octodex.github.com/images/homercat.png?width=20vw&lightbox=false)
{{% /notice %}}

{{% notice note %}}
If you want to wrap an image in a link and `lightbox=true` is your default setting, you have to explicitly disable the lightbox to avoid it to hijacking your link like:

````md
[![Homercat](https://octodex.github.com/images/homercat.png?lightbox=false)](https://octodex.github.com/#homercat)
````

[![Homercat](https://octodex.github.com/images/homercat.png?width=20vw&lightbox=false)](https://octodex.github.com/#homercat)

{{% /notice %}}
