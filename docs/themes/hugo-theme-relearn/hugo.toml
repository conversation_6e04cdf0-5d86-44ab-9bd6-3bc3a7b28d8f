[module]
  [module.hugoVersion]
    min = "0.121.0"

[mediaTypes]
  [mediaTypes."text/javascript"]
    suffixes = ["js"]

[outputFormats]
  [outputFormats.print]
    name= "print"
    baseName = "index.print"
    isHTML = true
    mediaType = 'text/html'
    permalinkable = false
    noUgly = true

  [outputFormats.search]
    name= "search"
    baseName = "index.search"
    isPlainText = true
    mediaType = 'text/javascript'
    permalinkable = false
    noUgly = true

  [outputFormats.searchpage]
    name= "searchpage"
    baseName = "search"
    isHTML = true
    mediaType = 'text/html'
    permalinkable = false
    noUgly = true

[params.relearn.dependencies]
  [params.relearn.dependencies.mathjax]
    name = "MathJax"
  [params.relearn.dependencies.mermaid]
    name = "Mermaid"
  [params.relearn.dependencies.openapi]
    name = "OpenA<PERSON>"
