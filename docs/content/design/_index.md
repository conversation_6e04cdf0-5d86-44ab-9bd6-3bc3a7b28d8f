---
title: "The Design of Athens"
date: 2018-09-20T15:37:49-07:00
weight: 3
---

This section of the documentation details the design of Athens. You can read the code and ask plenty of questions (which we're always happy to answer!), but we want to take some time here to give you a head start by describing how Athens is designed in words and diagrams, rather than code.

## What You'll Find Here

We've split this section into two major sections:

1. [Proxy internals](./proxy) - basics of the Athens proxy architecture and major features
2. [Communication flow](./communication) - how the Athens proxy interacts with the outside world to fetch and store code, respond to user requests, and so on

## How to Read this Section

We've designed the documentation in this section as a reference, which contrasts some of the other sections. That means that you don't need to read everything here from front to back to get value from it. We anticipate that you'll get the most out of this section while you're [contributing](/contributing) to the project.
