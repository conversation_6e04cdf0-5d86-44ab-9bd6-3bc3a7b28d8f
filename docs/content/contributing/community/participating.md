---
title: "Participating in the Community"
date: 2018-08-24T17:01:56-07:00
weight: 1

---

Absolutely everyone is welcome to join our community at any time! We
are a friendly and inclusive group and we'd love to have you. We have three
roles in the Athens community:

- Community member
- Contributor
- Maintainer

Read on to find out more!

# Community Members

Community members are folks who decide they want to get involved with our
community. Absolutely anyone can do that whenever they want. If you want
to get involved, that doesn't mean you have to commit to being involved, but
we hope our community is welcoming and the work is interesting enough to
convince you to stay :)

We'll provide all the support we can possibly provide to help you contribute
in any way you'd like. If you're considering joining us, here are some ideas
for how you can get involved:

- Comment on an [issue](https://github.com/gomods/athens/issues) that you're
interested in
- Submit a [pull request](https://github.com/gomods/athens/pulls) (PR) to fix
an issue, or to improve something that doesn't have an issue
- Review a PR that you're interested in
- Join us at [office hours](/contributing/community/office-hours/)
(or more than one!)
    - See [here](https://www.youtube.com/playlist?list=PLAk08AWjk5sekD-FRjU4VVe97nltUyZ4W) for recordings of all our past meetings
- Come chat with us in the [gophers slack](https://invite.slack.golangbridge.org/) in the `#athens` channel
- ... and anything else that's appropriate for you!

# Contributors

As you participate in the community more and more, you'll have the opportunity
to become a contributor. Here's what being a contributor means, and what you
should do to become one.

## What Being a Contributor Means

Contributors have read access to the Athens repository on Github. This means
that as a contributor, you're able to have issues assigned to you and you'll
be requested to review pull requests (PRs) via the
[Github pull request review system](https://help.github.com/articles/about-pull-request-reviews/).

We rely heavily on the Github PR review system, which means that if you review
a PR as a contributor, you can help decide when that PR is ready to be merged.
Don't worry that you _don't know enough_, the final approval and merge will be
by one or more maintainers.

## How to Become a Contributor

To become a contributor, the core maintainers of the project would like to see
you:

- Attend our development meetings regularly<sup>1</sup>
- Comment on issues with your experiences and opinions
- Add your comments and reviews on pull requests (anyone can do this as a
community member)
- Contribute PRs to fix issues
- Open issues as you find them

Contributors and maintainers will do their best to watch for community members
who may make good contributors. But don't be shy, if you feel that this is you,
please reach out to one or more of the contributors or maintainers.

# Maintainers

After you become a contributor, you'll have the opportunity to become a
maintainer. If you're happy with your contributions but want more of a role in
steering the project then read more on [being a maintainer](./contributing/maintainers).

# The End

The above descriptions lay out roughly what each role is and how you can
move into each of them. Folks all have different strengths, live
in different places, and so on. We're a diverse group, and we want to keep it
that way!

So, everything in this document is a guideline, not a hard-and-fast rule.
If you are really good at something, or can't do something else, talk to
one of the maintainers and let us know what's up. We will accommodate
everyone the best we can.

---
<p><i>
    <sup>1</sup> Athens development meetings are during the day in US Pacific Time.
    We know that this time can be problematic for some folks due to work commitments,
    different time zones, and so on. If you can't come to meetings, that's totally ok
    and doesn't mean you can't become a contributor! Just let one of the maintainers
    know about it, or leave a message in <code>#athens</code> in the <a href="https://invite.slack.golangbridge.org/">gophers slack</a>.
</i></p>
<p><i>
    <sup>2</sup> Anyone and everyone is of course welcome to do this too!
</i></p>
