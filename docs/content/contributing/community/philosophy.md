---
title: "Our Philosophy"
date: 2018-09-28T10:40:50-07:00
weight: 2

---

This document lays out generally how we want to work with each other. It's hard to make a rule or set a guideline for each and every situation that might come up in our community. That's basically predicting the future!

We do of course set some boundaries like the [code of conduct](https://github.com/gomods/athens/blob/main/CODE_OF_CONDUCT.md), but we want to fall back to this document for guidance when we encounter a new situation or question that we need to address.

# Guiding Principles

This is the TL;DR of the whole document! The Athens project has a few guiding principles:

- Be nice to each other
- Make development & testing easy
- Focus on the Community
- Ask Questions

In the rest of this document, we're going to go into detail for each of these items.

# Be Nice to Each Other

We firmly believe that a nice, welcoming and constructive community comes first, and code and technology second. If the folks in the community around this project aren't nice to each other, it doesn't matter how cool our technology is.

- Let's try to make newcomers feel welcome
- Let's put our debates in Github issues, and be civil and constructive in them
- Let's be empathetic
- Let's [listen more than we talk](https://hbr.org/2017/02/in-a-difficult-conversation-listen-more-than-you-talk)
- We're in different time zones, so let's respect that
- Let's encourage each other to learn new aspects of our project

Most importantly, let's **be inclusive**. Not everyone will share your point of view, communication style, and many other things. Try to consider their point of view and treat them with respect.

# Make Development & Testing Easy

Cognitive load is bad when you're writing code, so let's try to minimize it.

- Getting started should be a one-liner
- Let's make the hard things easier
- Docs are good, let's keep them up to date
- Let's make it pleasant to work with our code

# Focus on the Community

There's an African proverb that goes like this:

>If you want to go fast, go alone. If you want to go far, go together.

We want to apply that wisdom to our community.

- It's easy and sometimes tempting to do everything yourself
- But if we want to keep the project growing, it's hard to have just a few people doing everything
- Related: [the bus factor](https://en.wikipedia.org/wiki/Bus_factor)
- So let's focus on bringing new people into the community, and getting them started (see: Be Nice to Each Other, above)

# Ask Questions

Questions are a great way for us to share ideas and make the project and community better.

- If you don't know something, try not to be afraid to ask
- If you think your question is stupid, ask it anyway
- ... And if you're still uncomfortable asking in public, ask a maintainer in private
- If someone asks you a question, it's ok to answer it later
- Put answers on paper where everyone can read them, if you can
- ... And FAQs are great to have - let's do them!
- Newcomers have the best perspectives, so listen well to their questions
