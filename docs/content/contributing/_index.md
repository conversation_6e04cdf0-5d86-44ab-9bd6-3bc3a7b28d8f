---
title: "Contributing to Athens"
date: 2018-09-01T19:30:42-07:00
weight: 4

---

Welcome, Gopher! We're really glad you're considering contributing to Athens. We'd like to briefly introduce you to our community before you get started.

We have some hard-and-fast rules in our community, like our [Code of Conduct](https://github.com/gomods/athens/blob/main/CODE_OF_CONDUCT.md), but instead of making rules pre-emptively, we try to keep in mind a shared philosophy to help us all make decisions and make new rules when we need to.

## Our Philosophy Document

The [philosophy section](/contributing/community/philosophy/) of these docs details our philosophy, and if you get involved with our project we encourage you to read it. If you're just browsing for now, here's a brief summary:

- **Be Nice to Each Other** - people > code all the time, every time. We think that if we focus on our relationships with each other, we'll end up with better technology, better code, and a better community.
- **Make Development & Testing Easy** - Well, as easy as possible. This helps us reduce cognitive load so we can focus on Athens, not setting up our dev/test environment.
- **Focus on the Commmunity** - "If you want to go fast, go alone. If you want to go far, go together." We all try to follow this proverb. We try to get as many people involved as we can, we do (almost) everything 100% transparently, and we trust each other to do the right thing.

## Where to Go from Here

We hope you like what you see, and we'd love for you to get involved.

If you're familiar with Git and GitHub basics, read [how to participate in the Athens community](./community/participating) to learn about how we organize ourselves and how to get started.

If you're new to Git and GitHub, check out our [guide for new contributors to open source](./new), and then go on to that how to participate document I mentioned above.

Finally, if you are a new maintainer of the project, we have some documentation written for you at [./maintainers](./maintainers).
