---
title: "Docs"
date: 2018-11-01T13:58:58-07:00
weight: 2
LastModifierDisplayName: "<PERSON>wan"
LastModifierEmail: "<EMAIL>"

---

### Contributing To Docs

Contributing to docs is just as important, if not more important than, writing code. We use [GoHugo](https://gohugo.io/) to run this website. So if you'd like to improve it, here's how you can run it locally:

1. Install the Hugo binary: https://github.com/gohugoio/hugo#choose-how-to-install 
2. `cd ./docs && hugo server`

The Hugo server will run on http://localhost:1313 and it will automatically watch your files and update the website every time you make a change. 

Alternatively you can run our custom docker image as outlined [here](https://github.com/gomods/athens/blob/main/DEVELOPMENT.md#build-the-docs)