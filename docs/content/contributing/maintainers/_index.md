---
title: "Maintainers"
date: 2018-09-28T10:40:50-07:00
weight: 3

---

There are [a lot of ways to contribute to the Athens project](./participating) and being a maintainer is only one of those paths. Maintainers on the Athens Project are expected to
devote roughly five hours a week to the development and maintenance of the project. Their responsibilities include:

- Help organize our development meetings (i.e. help organize the agenda)
- Promote the project and build community (e.g. present on it where possible,
write about it, ...) when possible<sup>2</sup>
- Triage issues (e.g. adding labels, promoting discussions, finalizing
decisions)
- Organize and promote PR reviews (e.g. prompting community members,
contributors, and other maintainers to review)
- Help foster a safe and welcoming environment for all project participants.
This will include enforcing our code of conduct. We adhere to the [Contributor
Covenant](https://www.contributor-covenant.org), if you haven't read it yet you can do so [here](https://www.contributor-covenant.org/version/1/4/code-of-conduct) (english version).

We'd love to have your partnership in building Athens, but it is a statement to the commitment that a project of Athens' size and
complexity demands. If that appeals to you [join us on Slack](https://gophers.slack.com/archives/C9LRAQN8N).

## Benefits of being a maintainer

- Free access to GitHub CoPilot
- A rewarding sense of accomplishment
- Fun, complex work in a safe and collaborative environment
- Access to very smart people



## Contributing without maintaining

If maintaining Athens isn't something you can do, we're always looking for people to:
- Develop features
- Patch bugs
- Triage issues
- Provide support

You don't need to do anything other than visit our [GitHub page](https://github.com/gomods/athens) to do all of the above!