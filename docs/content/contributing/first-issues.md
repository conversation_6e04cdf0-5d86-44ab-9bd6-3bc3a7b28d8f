---
title: "Good First Issues"
date: 2018-09-02T10:00:00-07:00

---

The following is intended for project maintainers when triaging issues and
deciding to label them as a `good first issue`. It is meant to serve as a guide
on good practices only as each issue is different. This designation will be at
the discretion of a maintainer.

In this template we will assume this may be the contributor's first pull
request in The Athens Project.

> NOTE: Although this is written with maintainers in mind, anyone writing an
> issue with the goal of helping first time contributors is welcome to use this
> template as their guide.

---

Generally we will still want to try and follow the issue template for [bugs](https://github.com/gomods/athens/blob/main/.github/ISSUE_TEMPLATE/bug_report.md) or
[features](https://github.com/gomods/athens/blob/main/.github/ISSUE_TEMPLATE/feature_request.md). If you are performing issue triage you may need to add more
information to fulfill the below template.

## Template

### New to Athens?

If you are new to the project, and you haven't already, please take some time to
read our [contribution docs](https://docs.gomods.io/contributing).

### What needs to be done?

The issue should contain a detailed outline of the bug or feature.

This should include links to any relevant references both within and outside of
the project. Other issues, pull requests or comments. It could also include
relevant links to our docs or maybe blogs posts.

Try to include ideas of what good a starting place might be, or anything that
has been experimented with already.

#### New To Open Source

If you are new to open source, using git or GitHub, or just want some workflow
tips, head over to our [new contributor guide](https://docs.gomods.io/contributing/new).

