---
title: "Athens 101"
date: 2018-02-11T16:59:56-05:00
weight: 2
---

## 什么是 Athens?

简而言之，Athens 是一个建立在 vgo（或 go1.11+) 之上的项目，通过它你可以更容易地处理包的依赖问题，即使在 VCS 无法访问的时候，你也可以重新构建你的项目。

Athens 的宏伟目标是提供一个用于存放依赖（而不是代码）的新地方。因为在 GitHub 上，每一份元数据都对应着唯一不变的代码块，所以 Athens 只需要控制原数据的存储就够了。

你可能已经知道“不可变”的意义，但请让我再次说明，因为这一点对整个系统是非常重要的。当小伙伴改变了他们的包，迭代，实验，或者其他的事情，代码在 Athens 中永远不会变。如果包的作者发布了一个新版本，Athens 会把它拉取下来，并展现出来。因此，如果你依赖包 M 的版本 v1.2.3，那么它在 Athens 中就永远不会改变。_即使是强制推送或者是删除版本库，这都不会改变_。
