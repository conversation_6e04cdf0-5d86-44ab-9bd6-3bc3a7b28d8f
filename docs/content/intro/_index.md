---
title: "Introduction to Athens"
date: 2018-09-20T15:38:11-07:00
weight: 1
---

Welcome to Athens, Gophers! We gave a very brief overview of Athens on [the home page](/), so if you want to know more, you've come to the right place!

This section gives you all the details you need to understand what Athens does, why it exists, and how it fits into your workflow.

# Where to Go From Here

We recommend you read this section from front to back:

- [Athens 101](./first-content) - the basic mechanics of Athens
- [Download Protocol](./protocol) - the fundamental way that Athens serves Go packages
- [Components](./components) - the different moving pieces of Athens
