# Athens Stands on the Shoulders of Giants

Athens does not try to reinvent the wheel! Instead, it uses the already great wheels developed by the Go community and puts them all together in the best way possible. Without these giants, this project would not be possible. Please make sure to check them out and thank them for all of their hard work.

Thank you to the following **GIANTS**:


* [cloud.google.com/go](https://godoc.org/cloud.google.com/go)

* [contrib.go.opencensus.io/exporter/stackdriver](https://godoc.org/contrib.go.opencensus.io/exporter/stackdriver)

* [github.com/Azure/azure-storage-blob-go](https://godoc.org/github.com/Azure/azure-storage-blob-go)

* [github.com/BurntSushi/toml](https://godoc.org/github.com/BurntSushi/toml)

* [github.com/DataDog/datadog-go](https://godoc.org/github.com/DataDog/datadog-go)

* [github.com/DataDog/opencensus-go-exporter-datadog](https://godoc.org/github.com/DataDog/opencensus-go-exporter-datadog)

* [github.com/aws/aws-sdk-go](https://godoc.org/github.com/aws/aws-sdk-go)

* [github.com/fatih/color](https://godoc.org/github.com/fatih/color)

* [github.com/globalsign/mgo](https://godoc.org/github.com/globalsign/mgo)

* [github.com/go-playground/locales](https://godoc.org/github.com/go-playground/locales)

* [github.com/go-playground/universal-translator](https://godoc.org/github.com/go-playground/universal-translator)

* [github.com/gobuffalo/envy](https://godoc.org/github.com/gobuffalo/envy)

* [github.com/gobuffalo/httptest](https://godoc.org/github.com/gobuffalo/httptest)

* [github.com/gogo/protobuf](https://godoc.org/github.com/gogo/protobuf)

* [github.com/golang/glog](https://godoc.org/github.com/golang/glog)

* [github.com/google/go-cmp](https://godoc.org/github.com/google/go-cmp)

* [github.com/google/martian](https://godoc.org/github.com/google/martian)

* [github.com/googleapis/gax-go](https://godoc.org/github.com/googleapis/gax-go)

* [github.com/gopherjs/gopherjs](https://godoc.org/github.com/gopherjs/gopherjs)

* [github.com/gorilla/mux](https://godoc.org/github.com/gorilla/mux)

* [github.com/hashicorp/go-multierror](https://godoc.org/github.com/hashicorp/go-multierror)

* [github.com/jtolds/gls](https://godoc.org/github.com/jtolds/gls)

* [github.com/kelseyhightower/envconfig](https://godoc.org/github.com/kelseyhightower/envconfig)

* [github.com/konsorten/go-windows-terminal-sequences](https://godoc.org/github.com/konsorten/go-windows-terminal-sequences)

* [github.com/kr/pretty](https://godoc.org/github.com/kr/pretty)

* [github.com/mattn/go-colorable](https://godoc.org/github.com/mattn/go-colorable)

* [github.com/mattn/go-isatty](https://godoc.org/github.com/mattn/go-isatty)

* [github.com/minio/minio-go](https://godoc.org/github.com/minio/minio-go)

* [github.com/mitchellh/go-homedir](https://godoc.org/github.com/mitchellh/go-homedir)

* [github.com/philhofer/fwd](https://godoc.org/github.com/philhofer/fwd)

* [github.com/prometheus/client_golang](https://godoc.org/github.com/prometheus/client_golang)

* [github.com/sirupsen/logrus](https://godoc.org/github.com/sirupsen/logrus)

* [github.com/smartystreets/assertions](https://godoc.org/github.com/smartystreets/assertions)

* [github.com/smartystreets/goconvey](https://godoc.org/github.com/smartystreets/goconvey)

* [github.com/spf13/afero](https://godoc.org/github.com/spf13/afero)

* [github.com/stretchr/testify](https://godoc.org/github.com/stretchr/testify)

* [github.com/tinylib/msgp](https://godoc.org/github.com/tinylib/msgp)

* [github.com/unrolled/secure](https://godoc.org/github.com/unrolled/secure)

* [go.opencensus.io](https://godoc.org/go.opencensus.io)

* [golang.org/x/crypto](https://godoc.org/golang.org/x/crypto)

* [golang.org/x/net](https://godoc.org/golang.org/x/net)

* [golang.org/x/oauth2](https://godoc.org/golang.org/x/oauth2)

* [golang.org/x/sync](https://godoc.org/golang.org/x/sync)

* [golang.org/x/sys](https://godoc.org/golang.org/x/sys)

* [google.golang.org/api](https://godoc.org/google.golang.org/api)

* [google.golang.org/appengine](https://godoc.org/google.golang.org/appengine)

* [gopkg.in/DataDog/dd-trace-go.v1](https://godoc.org/gopkg.in/DataDog/dd-trace-go.v1)

* [gopkg.in/check.v1](https://godoc.org/gopkg.in/check.v1)

* [gopkg.in/go-playground/assert.v1](https://godoc.org/gopkg.in/go-playground/assert.v1)

* [gopkg.in/go-playground/validator.v9](https://godoc.org/gopkg.in/go-playground/validator.v9)
