[Unit]
Description=Athens Go module proxy
Documentation=https://docs.gomods.io/
After=network-online.target
Wants=network-online.target

[Service]
Restart=on-abnormal
Nice=5
;StartLimitInterval=86400
;StartLimitBurst=5

; User and group the process will run as.
User=www-data
Group=www-data

; The full path and the arguments of the command to be executed to start the process.
ExecStart=/usr/local/bin/athens -config_file=/etc/athens/config.toml

; The command necessary to stop the service.
;ExecStop=/bin/kill -INT $MAINPID

; The command necessary to reload the configuration of the service if available.
;ExecReload=/bin/kill -USR1 $MAINPID

; Use graceful shutdown with a reasonable timeout
KillMode=mixed
KillSignal=SIGINT
TimeoutStopSec=5s

; Limit the number of file descriptors; see `man systemd.exec` for more limit settings.
LimitNOFILE=1048576
; Unmodified athens is not expected to use more than that.
LimitNPROC=512

; Use private /tmp and /var/tmp, which are discarded after athens stops.
PrivateTmp=true

; Use a minimal /dev
PrivateDevices=true

; Prevent alteration of /home, /root, and /run/user.
ProtectHome=read-only

; Make /usr, /boot, /etc and possibly some more folders read-only.
ProtectSystem=full

; … except /var/run/athens, because we want disk storage here
;   This merely retains r/w access rights, it does not add any new. Must still be writable on the host!
ReadWritePaths=/var/run/athens

; The following additional security directives only work with systemd v229 or later.
; They further retrict privileges that can be gained by athens. Uncomment if you like.
; Note that you may have to add capabilities required by any plugins in use.
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE
NoNewPrivileges=true

; Ensure /var/log subdirectories are available. This is a space-separated list.
;LogsDirectory=

[Install]
WantedBy=multi-user.target
