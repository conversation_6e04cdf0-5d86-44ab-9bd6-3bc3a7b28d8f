version: 2
project_name: athens

before:
  hooks:
    - go mod download
env:
- CGO_ENABLED=0
- GOPROXY=https://proxy.golang.org

builds:
- id: builds
  goos:
    - linux
    - darwin
  goarch:
    - amd64
    - arm64
  main: cmd/proxy/main.go
  binary: athens
  ldflags:
    - -X "github.com/gomods/athens/pkg/build.version={{ .Tag }}"
    - -X "github.com/gomods/athens/pkg/build.buildDate={{ .Env.DATE }}"

checksum:
  name_template: 'checksums.txt'
snapshot:
  version_template: "{{ .Tag }}-next"
changelog:
  sort: asc
  filters:
    exclude:
    - '^docs:'
    - '^test:'
