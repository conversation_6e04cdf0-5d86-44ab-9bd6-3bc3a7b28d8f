[Settings]
    ID = "gomods-athens"

[build]
    base = "docs/"
    publish = "docs/public/"
    command = "hugo -v"
    environment = { HUGO_VERSION = "0.125.1" }

[[redirects]]
  from = "https://gomods.io/*"
  to = "https://docs.gomods.io/:splat"
  status = 303

[[redirects]]
  from = "http://gomods.io/*"
  to = "https://docs.gomods.io/:splat"
  status = 303

[[redirects]]
  from = "https://gomods-athens.netlify.com/*"
  to = "https://docs.gomods.io/:splat"
  status = 303
