package requestid

import "context"

// <PERSON><PERSON><PERSON><PERSON> is the header key that at<PERSON><PERSON> uses
// to pass request ids into logs and outbound requests.
const Head<PERSON><PERSON><PERSON> = "Athens-Request-ID"

type key struct{}

// SetInContext sets the given requestID into the context.
func SetInContext(ctx context.Context, id string) context.Context {
	return context.WithValue(ctx, key{}, id)
}

// FromContext returns a requestID from the context or an empty
// string if not found.
func FromContext(ctx context.Context) string {
	id, _ := ctx.Value(key{}).(string)
	return id
}
