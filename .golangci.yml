version: "2"
run:
  tests: false

formatters:
  enable:
    - gci
    - gofmt
    - gofumpt
    - goimports
  settings:
    gofumpt:
      extra-rules: true
  exclusions:
    generated: lax

linters:
  default: all
  disable:
    - cyclop
    - depguard
    - err113
    - errcheck
    - errchkjson
    - exhaustive
    - exhaustruct
    - forbidigo
    - forcetypeassert
    - funcorder
    - funlen
    - gochecknoglobals
    - gochecknoinits
    - gocognit
    - goconst
    - godox
    - intrange
    - ireturn
    - lll
    - mnd
    - musttag
    - nilnil
    - nlreturn
    - nonamedreturns
    - perfsprint
    - recvcheck
    - revive
    - rowserrcheck
    - spancheck
    - tagliatelle
    - testifylint
    - varnamelen
    - wrapcheck
    - wsl
  settings:
    cyclop:
      max-complexity: 12
    gosec:
      excludes:
        - G204
        - G402
  exclusions:
    generated: lax
    rules:
      - path: cmd/proxy/main.go
        text: 'G108: Profiling endpoint is automatically exposed on /debug/pprof'
      - linters:
          - contextcheck
        path: pkg/stash/stasher.go
      - linters:
          - bodyclose
        path: pkg/stash/with_azureblob.go
      - linters:
          - bodyclose
        path: pkg/storage/azureblob/azureblob.go
      - linters:
          - gosec
          - thelper
        path: pkg/storage/compliance/*
      - linters:
          - thelper
        path: pkg/index/compliance/*
      - path: (.+)\.go$
        text: 'package-comments: should have a package comment'
      - path: (.+)\.go$
        text: 'ST1000: at least one file in a package should have a package comment'
      - path: (.+)\.go$
        text: 'G115: integer overflow conversion int -> int32'
