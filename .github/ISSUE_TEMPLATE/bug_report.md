---
name: Bug report
about: Create a report to help us improve

---

**Describe the bug**
A clear and concise description of what the bug is.


**Error Message**
If applicable add error message to help explain your problem or console output enclose in triple back-ticks eg,

<pre>
```console
error message
and other output
```
</pre>


**To Reproduce**
Steps to reproduce the behavior:


**Expected behavior**
A clear and concise description of what you expected to happen.


**Environment (please complete the following information):**
 - OS: [e.g. Linux 64bit]
 - Go version :
 - Proxy version :
 - Storage (fs/mongodb/s3 etc.) :


**Additional context**
Add any other context about the problem here.
