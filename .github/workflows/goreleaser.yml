name: Publish Build Artifacts

on:
  push:
    tags:
      - '*'

jobs:
  goreleaser:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Go
        id: install-go
        uses: actions/setup-go@v5
        with:
          go-version-file: 'go.mod'

      - name: Download dependencies
        run: go mod download
        if: steps.install-go.outputs.cache-hit != 'true'

      - name: Capture Current Date
        id: date
        run: echo "::set-output name=date::$(date -u '+%Y-%m-%d-%H:%M:%S-%Z')"

      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          version: latest
          args: release --clean
        env:
          DATE: ${{ steps.date.outputs.date }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
