name: Release canary

on:
  push:
    branches:
      - main

jobs:
  docker-push-main:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64,linux/arm64

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Set Short SHA
        id: vars
        run: echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Build and push images
        uses: docker/build-push-action@v6
        with:
          file: cmd/proxy/Dockerfile
          build-args: VERSION=${{ github.sha }}
          tags: gomods/athens:canary,gomods/athens-dev:${{ steps.vars.outputs.sha_short }}
          push: true
          platforms: linux/amd64,linux/arm64
