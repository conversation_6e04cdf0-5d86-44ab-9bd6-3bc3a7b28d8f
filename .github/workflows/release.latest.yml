name: Release Docker Images

on:
  push:
    tags:
      - '**'

jobs:
  docker-push-main:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64,linux/arm64

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            gomods/athens
          tags: |
            type=raw,value=${{ github.ref_name }}
            type=raw,value=latest

      - name: Build and push images
        uses: docker/build-push-action@v6
        with:
          file: cmd/proxy/Dockerfile
          build-args: VERSION=${{ github.ref_name }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          push: true
          platforms: linux/amd64,linux/arm64
